# Анализ возможности добавления HTTP заголовков к краулеру Filin

## Что от вас хотят

Вам поставили задачу исследовать возможность добавления HTTP заголовков (в частности Authorization с Personal Access Token) к веб-краулеру Filin для индексации закрытых ресурсов, таких как Confluence.

**Цель**: Возможность передавать заголовок `Authorization: Bearer <PAT_TOKEN>` для доступа к приватным страницам Confluence при индексации.

## Текущая архитектура краулера в Filin

### Как устроена система источников данных

1. **Два типа веб-документов**:
   - **Custom Web Documents** - пользовательские источники, создаваемые администратором
   - **Preset Web Documents** - предустановленные источники (React, Vue.js, и др.)

2. **База данных** (`web_documents` таблица):
   ```sql
   CREATE TABLE web_documents(
       id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
       name VARCHAR(255) NOT NULL,
       url TEXT NOT NULL,
       is_preset BOOLEAN NOT NULL DEFAULT FALSE,
       created_at TIMESTAMP NOT NULL DEFAULT(DATETIME('now')),
       updated_at TIMESTAMP NOT NULL DEFAULT(DATETIME('now')),
       CONSTRAINT idx_name UNIQUE(name),
       CONSTRAINT idx_url UNIQUE(url)
   );
   ```

3. **GraphQL API для создания источников**:
   ```graphql
   createCustomDocument(input: CreateCustomDocumentInput!): ID!
   
   input CreateCustomDocumentInput {
     name: String!
     url: String!
   }
   ```

### Как работает краулер

1. **Используется внешний инструмент Katana** (Project Discovery):
   - Filin вызывает `katana` как внешний процесс
   - Код в `crates/tabby-crawler/src/lib.rs`
   - Katana запускается с различными флагами для контроля поведения

2. **Текущие параметры Katana**:
   ```rust
   command
       .arg("-u").arg(start_url)
       .arg("-jsonl")
       .arg("-crawl-scope").arg(format!("{}.*", regex::escape(prefix_url)))
       .arg("-crawl-out-scope").arg(r#"\.js$|\.css$|\.png$|\.jpg$|\.jpeg$"#)
       .arg("-depth").arg("9999")
       .arg("-max-response-size").arg("10485760") // 10MB
       .arg("-rate-limit-minute").arg("120")
       .arg("-strategy").arg("breadth-first")
   ```

3. **Два режима краулинга**:
   - **crawler_llms()** - для специальных файлов llms-full.txt
   - **crawl_pipeline()** - обычный краулинг через Katana

## Поддержка HTTP заголовков в Katana

**ХОРОШИЕ НОВОСТИ**: Katana уже поддерживает кастомные HTTP заголовки!

### Поддерживаемые флаги:
- `-H, -headers string[]` - кастомные заголовки в формате header:value
- Поддержка как отдельных заголовков, так и файлов с заголовками

### Примеры использования:
```bash
# Один заголовок
katana -u https://example.com -H 'Authorization: Bearer token123'

# Из файла
katana -u https://example.com -H headers.txt
```

### Файл с заголовками (`headers.txt`):
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
X-API-Key: your-api-key
Cookie: sessionid=abc123
```

## Что нужно сделать

### 1. Расширение базы данных

Добавить поле для HTTP заголовков в таблицу `web_documents`:

```sql
-- Новая миграция
ALTER TABLE web_documents ADD COLUMN http_headers TEXT;
```

### 2. Обновление DAO структуры

В `ee/tabby-db/src/web_documents.rs`:

```rust
#[derive(FromRow)]
pub struct WebDocumentDAO {
    pub id: i64,
    pub name: String,
    pub url: String,
    pub is_preset: bool,
    pub http_headers: Option<String>, // JSON строка с заголовками
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}
```

### 3. Обновление GraphQL схемы

В `ee/tabby-schema/src/schema/web_documents.rs`:

```rust
#[derive(Validate, GraphQLInputObject)]
pub struct CreateCustomDocumentInput {
    #[validate(regex(...))]
    pub name: String,
    #[validate(url(...))]
    pub url: String,
    pub http_headers: Option<Vec<HttpHeaderInput>>, // Новое поле
}

#[derive(GraphQLInputObject)]
pub struct HttpHeaderInput {
    pub name: String,
    pub value: String,
}
```

### 4. Обновление веб-интерфейса

В `ee/tabby-ui/` нужно добавить:
- Поля для ввода HTTP заголовков в форме создания источника
- UI для управления заголовками (add/remove)
- Валидацию заголовков

### 5. Обновление краулера

В `crates/tabby-crawler/src/lib.rs`:

```rust
pub async fn crawl_pipeline_with_headers(
    start_url: &str,
    prefix_url: &str,
    headers: Option<&[HttpHeader]>, // Новый параметр
) -> anyhow::Result<impl Stream<Item = CrawledDocument>> {
    let mut command = tokio::process::Command::new("katana");
    
    // ... существующие аргументы ...
    
    // Добавление заголовков
    if let Some(headers) = headers {
        for header in headers {
            command.arg("-H").arg(format!("{}:{}", header.name, header.value));
        }
    }
    
    // ... остальная логика ...
}
```

### 6. Обновление сервисов

В `ee/tabby-webserver/src/service/web_documents.rs`:

```rust
impl WebDocumentService for WebDocumentServiceImpl {
    async fn create_custom_web_document(
        &self, 
        name: String, 
        url: String,
        headers: Option<Vec<HttpHeader>>, // Новый параметр
    ) -> Result<ID> {
        let headers_json = headers.map(|h| serde_json::to_string(&h).unwrap());
        let id = self.db.create_web_document(name, url, false, headers_json).await?;
        
        // Передача заголовков в WebCrawlerJob
        let job = WebCrawlerJob::new_with_headers(
            CustomWebDocument::format_source_id(&id.as_id()),
            url,
            None,
            headers,
        );
        
        // ... остальная логика ...
    }
}
```

### 7. Обновление WebCrawlerJob

В `ee/tabby-webserver/src/service/background_job/web_crawler.rs`:

```rust
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WebCrawlerJob {
    source_id: String,
    pub url: String,
    url_prefix: Option<String>,
    http_headers: Option<Vec<HttpHeader>>, // Новое поле
}

impl WebCrawlerJob {
    pub fn new_with_headers(
        source_id: String, 
        url: String, 
        url_prefix: Option<String>,
        headers: Option<Vec<HttpHeader>>,
    ) -> Self {
        Self { source_id, url, url_prefix, http_headers: headers }
    }

    pub async fn run_impl(self, embedding: Arc<dyn Embedding>) -> tabby_schema::Result<()> {
        // Использование headers при вызове краулера
        let mut pipeline = Box::pin(
            crawl_pipeline_with_headers(&self.url, url_prefix, self.http_headers.as_deref()).await?
        );
        
        // ... остальная логика ...
    }
}
```

## Хранение заголовков

### Варианты хранения:

1. **JSON в TEXT поле** (рекомендуется):
   ```json
   [
     {"name": "Authorization", "value": "Bearer token123"},
     {"name": "X-API-Key", "value": "api-key-value"}
   ]
   ```

2. **Отдельная таблица** (для сложных случаев):
   ```sql
   CREATE TABLE web_document_headers(
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       web_document_id INTEGER REFERENCES web_documents(id),
       header_name VARCHAR(255),
       header_value TEXT,
       created_at TIMESTAMP DEFAULT(DATETIME('now'))
   );
   ```

## Безопасность

### Критически важно:
1. **Шифрование токенов** в базе данных
2. **Ограничение доступа** к заголовкам только для админов
3. **Валидация** названий заголовков
4. **Логирование** использования аутентифицированных запросов
5. **Ротация токенов** - механизм обновления

### Пример шифрования:
```rust
use ring::aead;

pub fn encrypt_header_value(value: &str, key: &aead::LessSafeKey) -> String {
    // Шифрование sensitive заголовков
}

pub fn decrypt_header_value(encrypted: &str, key: &aead::LessSafeKey) -> String {
    // Расшифровка при использовании
}
```

## UI/UX изменения

### Форма создания источника:
```tsx
// В ee/tabby-ui/components/
export function WebDocumentForm() {
  const [headers, setHeaders] = useState<HttpHeader[]>([]);
  
  return (
    <form>
      <input name="name" placeholder="Название источника" />
      <input name="url" placeholder="URL" />
      
      <div className="headers-section">
        <h3>HTTP Заголовки</h3>
        {headers.map((header, index) => (
          <div key={index} className="header-row">
            <input 
              placeholder="Название заголовка" 
              value={header.name}
              onChange={(e) => updateHeader(index, 'name', e.target.value)}
            />
            <input 
              placeholder="Значение"
              type={header.name === 'Authorization' ? 'password' : 'text'}
              value={header.value}
              onChange={(e) => updateHeader(index, 'value', e.target.value)}
            />
            <button onClick={() => removeHeader(index)}>Удалить</button>
          </div>
        ))}
        <button onClick={addHeader}>Добавить заголовок</button>
      </div>
    </form>
  );
}
```

## Confluence Integration

### Для Confluence потребуется:
1. **Personal Access Token** из Confluence
2. **Заголовок**: `Authorization: Bearer <PAT_TOKEN>`
3. **Базовый URL**: например, `https://oneproject.it-one.ru/confluence/`

### Пример создания источника:
```json
{
  "name": "IT-One Confluence",
  "url": "https://oneproject.it-one.ru/confluence/",
  "http_headers": [
    {
      "name": "Authorization",
      "value": "Bearer YOUR_PAT_TOKEN_HERE"
    }
  ]
}
```

## Тестирование

### Тесты для добавления:
1. **Unit тесты** для новых методов DAO
2. **Integration тесты** для GraphQL мутаций
3. **E2E тесты** для UI форм
4. **Тесты краулера** с заголовками

### Пример теста:
```rust
#[tokio::test]
async fn test_create_web_document_with_headers() {
    let db = DbConn::new_in_memory().await.unwrap();
    let headers = vec![
        HttpHeader { name: "Authorization".to_string(), value: "Bearer test".to_string() }
    ];
    
    let id = db.create_web_document_with_headers(
        "Test".to_string(),
        "https://example.com".to_string(),
        false,
        Some(headers)
    ).await.unwrap();
    
    let doc = db.get_web_document(id).await.unwrap();
    assert!(doc.http_headers.is_some());
}
```

## Миграция данных

### Новая миграция SQL:
```sql
-- ee/tabby-db/migrations/0xxx_add_http_headers.up.sql
ALTER TABLE web_documents ADD COLUMN http_headers TEXT;

-- ee/tabby-db/migrations/0xxx_add_http_headers.down.sql  
ALTER TABLE web_documents DROP COLUMN http_headers;
```

## Система миграций в Filin

### Как устроены миграции:
1. **Последовательная нумерация**: `0001_`, `0002_`, ..., `0047_`
2. **Парные файлы**:
   - `.up.sql` - применение миграции
   - `.down.sql` - откат миграции
3. **Расположение**: `ee/tabby-db/migrations/`
4. **Автоматическое применение** при запуске сервера

### Пример миграции:
```sql
-- 0048_add-web-document-headers.up.sql
ALTER TABLE web_documents ADD COLUMN http_headers TEXT;

-- 0048_add-web-document-headers.down.sql  
ALTER TABLE web_documents DROP COLUMN http_headers;
```

## Поля для добавления в базу данных

### Таблица `web_documents`:
```sql
-- Новое поле для хранения HTTP заголовков в JSON формате
http_headers TEXT,  -- JSON строка: [{"name":"Authorization","value":"Bearer token123"}]
```

### Альтернатива - отдельная таблица (если потребуется):
```sql
CREATE TABLE web_document_headers(
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    web_document_id INTEGER REFERENCES web_documents(id) ON DELETE CASCADE,
    header_name VARCHAR(255) NOT NULL,
    header_value TEXT NOT NULL,
    is_sensitive BOOLEAN DEFAULT FALSE, -- для шифрования Authorization и т.п.
    created_at TIMESTAMP DEFAULT(DATETIME('now')),
    updated_at TIMESTAMP DEFAULT(DATETIME('now'))
);
```

## Расширение External API (`ee/tabby-webserver/src/routes/external_groups`)

### Текущая архитектура External API:
- **Routes**: GET/POST `/sources`, GET/PUT/DELETE `/sources/{source_id}`
- **Models**: `CreateSourceRequest`, `UpdateSourceRequest`, `SourceData`
- **Handler**: через `GraphQLWrapper` в GraphQL мутации

### Новые поля в `SourceData` (`models.rs`):
```rust
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct SourceData {
    // ... существующие поля ...
    
    /// HTTP headers for web crawling (for doc sources only)
    #[serde(rename = "httpHeaders")]
    pub http_headers: Option<Vec<HttpHeaderData>>,
}

#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct HttpHeaderData {
    /// Header name (e.g. "Authorization", "X-API-Key")
    #[validate(length(min = 1, max = 100))]
    pub name: String,
    
    /// Header value (e.g. "Bearer token123")
    #[validate(length(min = 1, max = 1000))]
    pub value: String,
    
    /// Whether this header contains sensitive data
    #[serde(default)]
    pub is_sensitive: bool,
}
```

### Дополнительные API endpoints (безопасный подход):

#### 1. **Управление заголовками отдельно**:
```rust
// Новые роуты в mod.rs:
.route("/sources/{source_id}/headers", get(handlers::list_source_headers))
.route("/sources/{source_id}/headers", post(handlers::add_source_header))
.route("/sources/{source_id}/headers/{header_id}", put(handlers::update_source_header))
.route("/sources/{source_id}/headers/{header_id}", delete(handlers::delete_source_header))
```

#### 2. **Специальный endpoint для обновления заголовков**:
```rust
// POST /sources/{source_id}/update-headers
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct UpdateSourceHeadersRequest {
    #[validate(length(max = 10, message = "Maximum 10 headers allowed"))]
    pub headers: Vec<HttpHeaderData>,
}
```

### Преимущества отдельных endpoints:
- **Безопасность**: отдельная аутентификация для sensitive операций
- **Аудит**: отдельное логирование операций с заголовками
- **Гибкость**: возможность CRUD операций с отдельными заголовками
- **Обратная совместимость**: не ломает существующий API

## Задачи для JIRA (по порядку выполнения)

### 📋 Epic: "Добавление поддержки HTTP заголовков для краулера"

#### **FILIN-001: Исследование и планирование**
- **Описание**: Создать техническое ТЗ и план реализации
- **Задачи**:
  - Исследовать возможности Katana
  - Определить архитектуру решения
  - Создать план миграции БД
- **Приоритет**: High
- **Оценка**: 1 день

#### **FILIN-002: Создание миграции базы данных**
- **Описание**: Добавить поле `http_headers` в таблицу `web_documents`
- **Задачи**:
  - Создать файлы `0048_add-web-document-headers.up.sql` и `.down.sql`
  - Обновить `WebDocumentDAO` структуру
  - Обновить методы `create_web_document()` и `update_web_document()`
- **Файлы**: `ee/tabby-db/migrations/`, `ee/tabby-db/src/web_documents.rs`
- **Приоритет**: High  
- **Оценка**: 0.5 дня

#### **FILIN-003: Расширение External API - models**
- **Описание**: Добавить структуры данных для HTTP заголовков
- **Задачи**:
  - Добавить `HttpHeaderData` структуру в `models.rs`
  - Обновить `SourceData` с полем `http_headers`
  - Добавить `UpdateSourceHeadersRequest`
  - Обновить OpenAPI схемы (ToSchema)
- **Файлы**: `ee/tabby-webserver/src/routes/external_groups/models.rs`
- **Приоритет**: High
- **Оценка**: 0.5 дня

#### **FILIN-004: Расширение External API - handlers**
- **Описание**: Реализовать обработчики для работы с заголовками
- **Задачи**:
  - Добавить `update_source_headers()` handler
  - Добавить валидацию HTTP заголовков
  - Обновить существующие `create_source()` и `update_source()`
  - Добавить роуты в `mod.rs`
- **Файлы**: `ee/tabby-webserver/src/routes/external_groups/handlers.rs`, `mod.rs`
- **Приоритет**: High
- **Оценка**: 1 день

#### **FILIN-005: Обновление GraphQL wrapper**
- **Описание**: Интегрировать HTTP заголовки в GraphQL мутации
- **Задачи**:
  - Обновить `create_source()` в `graphql_wrapper.rs`
  - Добавить поддержку заголовков в WebDocument сервис
  - Передача заголовков в `WebCrawlerJob`
- **Файлы**: `ee/tabby-webserver/src/routes/external_groups/graphql_wrapper.rs`
- **Приоритет**: High
- **Оценка**: 1 день

#### **FILIN-006: Обновление WebDocument сервиса**
- **Описание**: Добавить поддержку HTTP заголовков в WebDocument сервис
- **Задачи**:
  - Обновить `create_custom_web_document()` метод
  - Добавить параметр `headers` в методы сервиса
  - Обновить структуры `CustomWebDocument`
- **Файлы**: `ee/tabby-webserver/src/service/web_documents.rs`, `ee/tabby-schema/src/schema/web_documents.rs`
- **Приоритет**: High
- **Оценка**: 1 день

#### **FILIN-007: Интеграция с краулером**
- **Описание**: Передача HTTP заголовков в Katana процесс
- **Задачи**:
  - Обновить `WebCrawlerJob` структуру
  - Добавить поле `http_headers` в job
  - Модифицировать `crawl_pipeline()` для передачи `-H` флагов в Katana
  - Обновить `crawler_llms()` для поддержки заголовков
- **Файлы**: `ee/tabby-webserver/src/service/background_job/web_crawler.rs`, `crates/tabby-crawler/src/lib.rs`
- **Приоритет**: High
- **Оценка**: 1.5 дня

#### **FILIN-008: Добавление безопасности**
- **Описание**: Шифрование sensitive заголовков
- **Задачи**:
  - Реализовать шифрование/дешифровку токенов
  - Добавить поле `is_sensitive` для заголовков
  - Добавить валидацию заголовков
  - Обеспечить secure логирование (без токенов в логах)
- **Файлы**: Новый модуль `header_encryption.rs`
- **Приоритет**: High
- **Оценка**: 1 день

#### **FILIN-009: Обновление веб-интерфейса**
- **Описание**: UI для управления HTTP заголовками
- **Задачи**:
  - Создать компонент `HttpHeadersEditor`
  - Обновить форму создания источников
  - Добавить валидацию на фронтенде
  - Добавить поддержку `password` типа для sensitive заголовков
- **Файлы**: `ee/tabby-ui/components/`
- **Приоритет**: Medium
- **Оценка**: 2 дня

#### **FILIN-010: Тестирование**
- **Описание**: Покрытие тестами новой функциональности
- **Задачи**:
  - Unit тесты для DAO методов
  - Integration тесты для External API
  - E2E тесты для UI
  - Тесты краулера с заголовками
  - Тесты безопасности (шифрование)
- **Файлы**: Различные test файлы
- **Приоритет**: High
- **Оценка**: 1.5 дня

#### **FILIN-011: Документация и деплой**
- **Описание**: Обновление документации и подготовка к релизу
- **Задачи**:
  - Обновить API документацию
  - Создать user guide по настройке заголовков
  - Обновить CHANGELOG
  - Подготовить migration guide
- **Файлы**: `README.md`, `docs/`, `CHANGELOG.md`
- **Приоритет**: Medium
- **Оценка**: 0.5 дня

### 📊 **Общая оценка времени: 10.5 дней (2 недели)**

## Confluence Integration - Готовое решение

После реализации, для настройки Confluence потребуется:

### 1. Получить PAT токен:
- Перейти в Confluence: https://oneproject.it-one.ru/confluence/plugins/personalaccesstokens/usertokens.action
- Создать новый токен

### 2. Создать источник через API:
```bash
curl -X POST "/v1/external-groups/sources" \
  -H "Authorization: Bearer admin_token" \
  -H "Content-Type: application/json" \
  -d '{
    "source_type": "doc",
    "data": {
      "type": "doc", 
      "name": "IT-One Confluence",
      "url": "https://oneproject.it-one.ru/confluence/",
      "httpHeaders": [
        {
          "name": "Authorization",
          "value": "Bearer YOUR_PAT_TOKEN_HERE",
          "is_sensitive": true
        }
      ]
    }
  }'
```

### 3. Или через веб-интерфейс:
- **Name**: "IT-One Confluence"
- **URL**: "https://oneproject.it-one.ru/confluence/"
- **HTTP Headers**:
  - Header Name: `Authorization`
  - Header Value: `Bearer YOUR_PAT_TOKEN` 
  - Is Sensitive: ✅

## Заключение

Реализация HTTP заголовков для краулера Filin **полностью осуществима** и **не требует значительных архитектурных изменений**. Katana уже поддерживает все необходимые функции.

### Ключевые преимущества подхода:
- ✅ **Обратная совместимость** - не ломает существующий API
- ✅ **Безопасность** - шифрование sensitive заголовков  
- ✅ **Гибкость** - отдельные endpoints для управления заголовками
- ✅ **Тестируемость** - четкое разделение ответственности

### Риски: **Низкие** 
- Katana уже поддерживает HTTP заголовки
- External API имеет готовую архитектуру для расширения
- Система миграций проста и надежна

### Время реализации: **2 недели** (10.5 дней)
Основная работа будет связана с **backend разработкой** и **интеграцией**, **frontend** займет ~20% времени.