

*Author: Cascade AI – 2025-08-01*

---

## 1. What the Stakeholders Are Asking

The team needs to **index private Confluence pages**.  Confluence supports Personal Access Tokens (PAT).  The token must be sent in the HTTP request header:

```
Authorization: Bearer <PAT>
```

Today <PERSON>lin’s web-crawler runs **without any custom headers**, therefore it is blocked by authentication and cannot reach private resources.  We must evaluate how hard it is to “inject” headers and outline the changes required to support it.

---

## 2. Current Crawling Architecture (TL;DR)

| Layer | File / Crate | Purpose |
|-------|--------------|---------|
| **Source CRUD** | `ee/tabby-webserver/src/routes/external_groups` | REST & GraphQL endpoints that create “sources” (git / doc etc.) |
| **Web Document Service** | `ee/tabby-webserver/src/service/web_documents.rs` | Persists custom/preset web-docs in SQLite (`web_documents` table).  Triggers background crawl jobs. |
| **Background Job** | `ee/tabby-webserver/src/service/background_job/web_crawler.rs` (`WebCrawlerJob`) | Converts Source → crawl pipeline.  Calls the crawler library. |
| **Crawler Library** | `crates/tabby-crawler` | Wrapper around [`katana`](https://github.com/projectdiscovery/katana) CLI; also fetches `llms-full.txt` via `reqwest`. |

> **Important:** None of the layers above accept or forward request headers.  `katana` does support `-H "Header: value"`, but Filin never uses it.

### 2.1  Source Type “doc” (a.k.a. Developer Docs)

* UI path: **Settings ➜ Context Sources ➜ Developer Docs**  
* DB table: `web_documents`  
  * columns: `id, name, url, preset BOOLEAN, created_at, updated_at`
* REST endpoint: `POST /v1/external-groups/sources` with `source_type="doc"`
* Background trigger: `WebCrawlerJob::new(source_id, url, None)`

---

## 3. Gap Analysis – Why Headers Are Impossible Today

1. **Schema limitation** – `SourceData` and the `web_documents` table contain only `name`, `url`, and flags.  No place to store headers.
2. **Job interface** – `WebCrawlerJob` only knows `(source_id, url, url_prefix)`.
3. **Crawler call** – `crawl_url()` in `tabby-crawler` builds the `katana` command without `-H`.
4. **`llms-full.txt` fetch** – separate `reqwest::get()` call also lacks headers.

---

## 4. Design Proposal – Adding Header Support

### 4.1  Data Model Changes

1. **Database**  
   Add a new `headers` column to `web_documents` (JSON string; nullable).

   ```sql
   ALTER TABLE web_documents ADD COLUMN headers TEXT; -- JSON encoded {"Header":"Value"}
   ```

2. **Rust models**  
   * `ee/.../models.rs -> SourceData`
   * `WebDocumentDAO`, DTOs, GraphQL types

   Add field:

   ```rust
   /// Optional extra HTTP headers (JSON object)
   pub headers: Option<HashMap<String, String>>,
   ```

3. **Source REST / GraphQL**  
   Extend create / update endpoints to accept `headers`.

### 4.2  Background Job API

```rust
pub struct WebCrawlerJob {
    source_id: String,
    url: String,
    url_prefix: Option<String>,
    headers: Option<HashMap<String, String>>, // NEW
}
```

▶ Propagate from `WebDocumentService` when triggering the job.

### 4.3  Crawler Layer

1. **`tabby-crawler::crawl_url()`**  
   Accept `headers: &HashMap<…>` and append:

   ```rust
   for (k, v) in headers {
       command.arg("-H").arg(format!("{}: {}", k, v));
   }
   ```

2. **`reqwest` fetch in `crawler_llms()`**  
   Replace `reqwest::get()` with a `Client::builder().default_headers(headers.clone()).build()?`.

### 4.4  UI Updates (Optional MVP)

* Because the Admin UI currently shows only *Name* & *URL*, add an **Advanced** accordion with key-value header inputs.
* Until UI is ready, power-users can use the REST API directly.

### 4.5  Migration & Backwards Compatibility

* `headers` defaults to `NULL` → no behaviour change for existing sources.
* Feature flag env var `FILIN_ENABLE_CRAWLER_HEADERS` can guard the new path.

---

## 5. Impacted Tables & Code Locations

| Component | File / Table | Action |
|-----------|--------------|--------|
| DB | `web_documents` | add `headers` TEXT |
| Rust model | `SourceData`, `WebDocumentDAO` | new field |
| Service | `web_documents.rs` | persist field; pass to job |
| BG Job | `web_crawler.rs` | struct & serialization |
| Crawler | `tabby-crawler/lib.rs` | CLI args; reqwest headers |
| REST API | `external_groups/handlers.rs` | validate & expose |
| UI | `web/src/pages/DeveloperDocs*` | optional enhancement |

---

## 6. Estimated Effort

| Task | Complexity |
|------|------------|
| DB migration + model plumbing | **M** (≈1-2 h) |
| Job / crawler refactor | **M** (≈2-3 h) |
| REST + validation updates | **M** (≈1-2 h) |
| UI changes | **S** (optional) |
| Tests (unit + e2e crawler) | **M** |

Overall **1-2 days** of engineering work.

---

## 7. Quick Work-Around (No Code Change)

If Confluence allows **Basic-Auth in URL**, you could create the source with an *access-token-embedded URL*:

```
https://<user>:<PAT>@confluence.example.com/rest/api/content/1234
```

…but many servers disable this pattern, and credentials would be stored in plain-text, so the **proper header solution above is preferred**.

---

## 8. Next Steps

1. Approve the schema change & header field API.
2. Implement the backend changes (sections 4.1–4.3) behind a feature flag.
3. Add integration test with a mock HTTP server requiring an `Authorization` header.
4. Optional: extend Admin UI.

---

*End of document*

# Внедрение пользовательских HTTP-заголовков в веб-краулер Filin

*Автор: Cascade AI – 01 авг 2025 г.*

---

## 1. Что требуется

Нужно индексировать приватные страницы Confluence. Confluence принимает Personal Access Token (PAT) в HTTP-заголовке:

```
Authorization: Bearer <PAT>
```

Сейчас веб-краулер Filin **не** отправляет кастомные заголовки, поэтому не может получить защищённый контент. Требуется оценить, как добавить заголовки, и описать необходимые изменения.

---

## 2. Текущая архитектура краулинга (кратко)

| Слой | Файл/крейт | Назначение |
|------|-----------|------------|
| **CRUD источников** | `ee/tabby-webserver/src/routes/external_groups` | REST/GraphQL-эндпоинты создания «источников» (git, doc и пр.) |
| **Сервис веб-документов** | `ee/tabby-webserver/src/service/web_documents.rs` | Хранит web-documents в SQLite (`web_documents`) и запускает фоновые джобы краулера |
| **Фоновая джоба** | `ee/tabby-webserver/src/service/background_job/web_crawler.rs` (`WebCrawlerJob`) | Преобразует Source → краулинг, вызывает библиотеку краулера |
| **Библиотека краулера** | `crates/tabby-crawler` | Обёртка над [`katana`](https://github.com/projectdiscovery/katana); также скачивает `llms-full.txt` через `reqwest` |

> **Важно:** ни один слой не принимает и не передаёт заголовки. При этом `katana` умеет `-H "Header: value"`, но Filin не использует это.

### 2.1 Источник типа “doc” (Developer Docs)

* UI: **Settings → Context Sources → Developer Docs**  
* Таблица БД: `web_documents` (`id, name, url, preset, created_at, updated_at`)  
* REST: `POST /v1/external-groups/sources` с `source_type="doc"`  
* Фоновый запуск: `WebCrawlerJob::new(source_id, url, None)`

---

## 3. Пробелы — почему заголовки невозможны сейчас

1. **Схема** — в `SourceData` и таблице `web_documents` есть только `name`, `url`, без поля заголовков.  
2. **Интерфейс джобы** — `WebCrawlerJob` знает лишь `(source_id, url, url_prefix)`.  
3. **Вызов краулера** — `crawl_url()` формирует команду `katana` без `-H`.  
4. **Скачивание `llms-full.txt`** — отдельный `reqwest::get()` тоже без заголовков.

---

## 4. Предложение по реализации поддержки заголовков

### 4.1 Изменения модели данных

1. **База данных**  
   Добавить колонку `headers` (TEXT, JSON):

   ```sql
   ALTER TABLE web_documents ADD COLUMN headers TEXT; -- {"Header":"Value"}
   ```

2. **Rust-модели** (`SourceData`, `WebDocumentDAO` и др.):

   ```rust
   /// Дополнительные HTTP-заголовки
   pub headers: Option<HashMap<String, String>>,
   ```

3. **REST / GraphQL** — расширить create/update, чтобы принимать `headers`.

### 4.2 API фоновой джобы

```rust
pub struct WebCrawlerJob {
    source_id: String,
    url: String,
    url_prefix: Option<String>,
    headers: Option<HashMap<String, String>>, // НОВОЕ
}
```

Передавать из `WebDocumentService`.

### 4.3 Слой краулера

1. **`tabby-crawler::crawl_url()`** — принять `headers` и добавить к `katana`:

   ```rust
   for (k, v) in headers {
       command.arg("-H").arg(format!("{k}: {v}"));
   }
   ```

2. **`crawler_llms()`** — использовать `reqwest::Client` с `default_headers`.

### 4.4 UI (опционально)

* В форме источника добавить секцию **Advanced** с полями «Header / Value».
* Пока UI нет, можно работать через REST API.

### 4.5 Миграция и обратная совместимость

* `headers` по умолчанию `NULL`, поведение старых источников не меняется.
* Можно защитить фичу флагом `FILIN_ENABLE_CRAWLER_HEADERS`.

---

## 5. Затрагиваемые компоненты

| Компонент | Файл/таблица | Действие |
|-----------|--------------|----------|
| DB | `web_documents` | колонка `headers` |
| Модели | `SourceData`, `WebDocumentDAO` | новое поле |
| Сервис | `web_documents.rs` | сохранение и передача |
| Джоба | `web_crawler.rs` | поле + сериализация |
| Краулер | `tabby-crawler/lib.rs` | CLI `-H`, заголовки в `reqwest` |
| REST | `external_groups/handlers.rs` | валидация/экспонирование |
| UI | страницы Developer Docs | при необходимости |

---

## 6. Оценка трудозатрат

| Задача | Сложность |
|--------|-----------|
| Миграция + модели | **M** (≈1–2 ч) |
| Рефактор джобы/краулера | **M** (≈2–3 ч) |
| REST + валидация | **M** (≈1–2 ч) |
| UI | **S** (опц.) |
| Тесты (unit + e2e) | **M** |

Всего **1–2 дня** работы.

---

## 7. Быстрый обходной путь (без кода)

Если Confluence допускает **Basic-Auth в URL**, можно указать токен в ссылке:

```
https://<user>:<PAT>@confluence.example.com/rest/api/content/1234
```

Но это небезопасно, так что лучше реализовать заголовки.

---

## 8. Следующие шаги

1. Утвердить схему и API поля `headers`.  
2. Реализовать backend (п. 4.1–4.3) под фиче-флагом.  
3. Добавить интеграционный тест с мок-сервером, требующим `Authorization`.  
4. (Опц.) Расширить UI.

---

---

## Правила миграций в Filin

Filin использует **sqlx**-migrate. Каждая миграция состоит из двух файлов в каталоге `ee/tabby-db/migrations`:

* `<NNNN>_<slug>.up.sql` — изменения схемы **вперёд**.
* `<NNNN>_<slug>.down.sql` — откат.

Файлы нумеруются строго по возрастанию, четыре цифры. Названия должны быть короткими и описательными, например `0048_web_document_headers`.

При добавлении новой миграции нужно:

1. Создать оба файла в том же каталоге.
2. Обновить CI, если требуется (pipeline автоматически применяет все `.up.sql`).
3. Убедиться, что `down.sql` корректно откатывает изменения.

---

## Изменения в базе данных

| Таблица | Поле | Тип | Описание |
|---------|------|-----|----------|
| `web_documents` | `headers` | `TEXT` (nullable, JSON) | Сериализованная карта заголовков. Пример: `{ "Authorization": "Bearer …" }` |

> Для обратной совместимости колонка допускает `NULL`.

---

## Изменения внешнего API (`external_groups`)

1. **Расширить существующий `PUT /v1/external-groups/sources/{id}`**  
   • добавить опциональное поле `headers` в тело запроса.  
   • если поле отсутствует — поведение прежнее.  
   • валидировать, что JSON-объект строк-строк.

2. **Новый эндпоинт** `PATCH /v1/external-groups/sources/{id}/headers`  
   • принимает JSON-объект заголовков.  
   • перезаписывает колонку `headers`.  
   • возвращает обновлённый объект Source.  
   • удобно для UI, не затрагивает другие поля.

> Реализация находится в `ee/tabby-webserver/src/routes/external_groups/handlers.rs`. Нужно дополнить модели запросов/ответов в `models.rs` и обновить OpenAPI-аннотации.

---

## Задачи для Jira (порядок выполнения)

1. **Миграция `0048_web_document_headers`**  
   • `up.sql`: `ALTER TABLE web_documents ADD COLUMN headers TEXT;`  
   • `down.sql`: `ALTER TABLE web_documents DROP COLUMN headers;`
2. **Модели / DAO / GraphQL** — добавить поле `headers` (nullable `HashMap<String,String>`).
3. **Расширить API**  
   • обновить `UpdateSourceRequest`  
   • реализовать `PATCH /sources/{id}/headers`.
4. **Обновить `WebDocumentService`** — сохранять `headers` и передавать их в `WebCrawlerJob`.
5. **Расширить `WebCrawlerJob` + сериализацию** — новое поле `headers`.
6. **tabby-crawler**  
   • добавить поддержку CLI `-H`  
   • использовать `reqwest::Client::builder().default_headers()`.
7. **Тесты**  
   • unit: сохранение/загрузка заголовков из БД  
   • e2e: мок-сервер, требующий `Authorization`.
8. **(Опц.) UI** — форма ввода заголовков.
9. **Документация** — обновить README/Swagger.

---

*Конец документа (RU)*
