# Техническое Задание: Поддержка HTTP Заголовков в Краулере Filin (Версия 1.0)

**Версия документа:** 1.0  
**Дата обновления:** Август 2024  
**Целевая аудитория:** Разработчики, системные архитекторы, тимлиды  
**Статус:** 🎯 **К реализации**

---

### 📋 TL;DR - Краткий обзор (1 минута)

**Что это:** Добавление поддержки кастомных HTTP-заголовков в веб-краулер Filin.  
**Цель:** Индексация закрытых ресурсов (например, Confluence) с использованием токенов авторизации (`Authorization: Bearer <PAT>`).  
**Архитектура:** Модификация 5 уровней системы (UI → GraphQL → БД → Background Job → Краулер).  
**Ключевая технология:** Использование встроенной поддержки заголовков (`-H` флаг) во внешнем краулере **Katana**.  
**Сложность:** ⚠️ **Средняя**. Реализация займет ~2 недели.  
**Риски:** Низкие, т.к. основной инструмент (Katana) уже поддерживает функционал.

---

### 📑 Содержание

<details>
<summary><strong>🏗️ АРХИТЕКТУРА И РЕШЕНИЕ</strong></summary>

- [Обзор проекта и задачи](#обзор-проекта-и-задачи)
- [Концептуальная диаграмма изменений](#концептуальная-диаграмма-изменений)
- [C4 Модель: Контейнеры (текущее состояние)](#c4-модель-контейнеры-текущее-состояние)
- [Ключевые структуры данных](#ключевые-структуры-данных)
- [Процесс обработки с заголовками (Sequence Diagram)](#процесс-обработки-с-заголовками-sequence-diagram)

</details>

<details>
<summary><strong>🔧 ПЛАН РЕАЛИЗАЦИИ</strong></summary>

- [Детальный план задач для Jira](#детальный-план-задач-для-jira)
- [Изменения в базе данных](#изменения-в-базе-данных)
- [Изменения в External API и GraphQL](#изменения-в-external-api-и-graphql)
- [Изменения в краулере](#изменения-в-краулере)
- [Изменения в UI](#изменения-в-ui)

</details>

<details>
<summary><strong>🔒 БЕЗОПАСНОСТЬ</strong></summary>

- [Анализ угроз и меры по их снижению](#анализ-угроз-и-меры-по-их-снижению)
- [Хранение и шифрование токенов](#хранение-и-шифрование-токенов)

</details>

---

### Обзор проекта и задачи

**Проблема:** Веб-краулер Filin не может индексировать контент, защищенный аутентификацией (например, корпоративный Confluence), так как не умеет отправлять кастомные HTTP-заголовки, в частности заголовок `Authorization`.

**Цель:** Реализовать сквозную передачу HTTP-заголовков от пользовательского интерфейса до процесса краулинга, чтобы обеспечить доступ к закрытым веб-ресурсам.

**Текущий статус:**
- ✅ **Katana (внешний краулер):** Уже поддерживает передачу заголовков через флаг `-H`.
- 🚧 **Архитектура Filin:** Требует доработки на всех уровнях для хранения и передачи заголовков.

### Концептуальная диаграмма изменений

*Высокоуровневое представление компонентов, которые будут затронуты.*

```mermaid
graph TD
    subgraph "🎬 Пользовательский слой"
        UI[Web-интерфейс]
    end
    
    subgraph "⚡ Слой API и сервисов"
        GQL[GraphQL API]
        API[External REST API]
        SVC[WebDocument Service]
    end
    
    subgraph "⚙️ Слой данных и задач"
        DB[База данных (SQLite)]
        JOB[WebCrawlerJob (фоновая задача)]
    end
    
    subgraph "🤖 Слой краулинга"
        CRAWLER[tabby-crawler]
        KATANA[Katana CLI]
    end

    UI -- "Добавить поле 'headers'" --> GQL
    API -- "Добавить поле 'headers'" --> SVC
    GQL --> SVC
    SVC -- "Сохранить 'headers'" --> DB
    SVC -- "Передать 'headers'" --> JOB
    JOB -- "Передать 'headers'" --> CRAWLER
    CRAWLER -- "Добавить флаг -H" --> KATANA

    classDef changed fill:#fff3e0,stroke:#f57c00,stroke-width:3px
    class UI,GQL,API,SVC,DB,JOB,CRAWLER,KATANA changed
```

### C4 Модель: Контейнеры (текущее состояние)

```mermaid
graph TB
    subgraph "🎯 Filin Platform"
        subgraph "🖥️ Application Layer"
            UI[📱 Web UI<br/>React-приложение]
            API_GQL[🌐 GraphQL API<br/>Async-graphql]
            API_EXT[🔌 External REST API<br/>Axum]
        end
        
        subgraph "⚡ Service & Job Layer"
            WEBDOC_SVC[📝 WebDocument Service<br/>Бизнес-логика]
            JOB_SVC[🔄 Background Job Service<br/>Запуск задач]
        end
        
        subgraph "🤖 Crawler Layer" 
            CRAWLER_LIB[🦀 tabby-crawler<br/>Обертка над Katana]
            KATANA_CLI[⚙️ Katana CLI<br/>Внешний процесс]
        end
        
        subgraph "💾 Data Layer"
            DB[🗃️ SQLite Database<br/>Таблица web_documents]
            JOB_QUEUE[📨 Job Queue<br/>Сериализованные задачи]
        end
    end
    
    UI --> API_GQL
    API_EXT --> WEBDOC_SVC
    API_GQL --> WEBDOC_SVC
    WEBDOC_SVC --> DB
    WEBDOC_SVC --> JOB_SVC
    JOB_SVC --> JOB_QUEUE
    JOB_QUEUE --> CRAWLER_LIB
    CRAWLER_LIB --> KATANA_CLI
```

### Ключевые структуры данных

#### 1. База данных (`web_documents`)
```sql
ALTER TABLE web_documents ADD COLUMN http_headers TEXT;
-- Хранит JSON-строку: '[{"name": "Authorization", "value": "Bearer token"}]'
```

#### 2. GraphQL Input
```rust
#[derive(GraphQLInputObject)]
pub struct HttpHeaderInput {
    pub name: String,
    pub value: String,
}

#[derive(Validate, GraphQLInputObject)]
pub struct CreateCustomDocumentInput {
    pub name: String,
    pub url: String,
    pub http_headers: Option<Vec<HttpHeaderInput>>, // Новое поле
}
```

#### 3. Фоновая задача (`WebCrawlerJob`)
```rust
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WebCrawlerJob {
    source_id: String,
    pub url: String,
    url_prefix: Option<String>,
    http_headers: Option<Vec<HttpHeader>>, // Новое поле
}
```

### Процесс обработки с заголовками (Sequence Diagram)

```mermaid
sequenceDiagram
    participant User as 👤 Пользователь
    participant UI as 🖥️ Web UI
    participant GQL as ⚡ GraphQL
    participant Service as 📝 WebDoc Service
    participant DB as 🗃️ Database
    participant Job as 🔄 WebCrawlerJob
    participant Crawler as 🦀 Crawler Lib
    participant Katana as ⚙️ Katana

    User->>+UI: 1. Вводит URL и заголовки
    UI->>+GQL: 2. createCustomDocument(input)
    GQL->>+Service: 3. create_custom_web_document()
    Service->>+DB: 4. Сохранить документ с заголовками
    DB-->>-Service: ID документа
    Service->>+Job: 5. Создать WebCrawlerJob с заголовками
    Job-->>-Service: Job создан
    Service-->>-GQL: Успех
    GQL-->>-UI: Успех
    UI-->>-User: Источник создан

    Note over Job, Katana: Позже, в фоновом режиме...
    
    Job->>+Crawler: 6. run_impl()
    Crawler->>+Crawler: 7. Вызвать crawl_pipeline_with_headers()
    Crawler->>+Katana: 8. Запустить `katana -u ... -H "Auth:..."`
    Katana-->>-Crawler: 9. Поток данных (JSONL)
    Crawler-->>-Job: 10. Обработанные документы
```

### Детальный план задач для Jira

> 📊 **Общая оценка:** 10.5 дней (2 недели)

<details>
<summary><strong>📋 Epic: "Добавление поддержки HTTP заголовков для краулера"</strong></summary>

- **FILIN-001: Исследование и планирование** (1 день)
- **FILIN-002: Создание миграции базы данных** (0.5 дня)
  - `ALTER TABLE web_documents ADD COLUMN http_headers TEXT;`
  - Обновить `WebDocumentDAO`.
- **FILIN-003: Расширение External API - models** (0.5 дня)
  - Добавить `HttpHeaderData` и обновить `SourceData`.
- **FILIN-004: Расширение External API - handlers** (1 день)
  - Реализовать обработчики для CRUD операций с заголовками.
- **FILIN-005: Обновление GraphQL wrapper** (1 день)
  - Интегрировать заголовки в GraphQL мутации.
- **FILIN-006: Обновление WebDocument сервиса** (1 день)
  - Обновить `create_custom_web_document()` для приема и сохранения заголовков.
- **FILIN-007: Интеграция с краулером** (1.5 дня)
  - Обновить `WebCrawlerJob` и `crawl_pipeline()` для передачи флагов `-H` в Katana.
- **FILIN-008: Добавление безопасности** (1 день)
  - Реализовать шифрование/дешифровку токенов в БД.
  - Добавить маскирование токенов в логах.
- **FILIN-009: Обновление веб-интерфейса** (2 дня)
  - Создать UI для добавления/удаления key-value пар заголовков.
- **FILIN-010: Тестирование** (1.5 дня)
  - Unit, Integration, E2E тесты.
- **FILIN-011: Документация и деплой** (0.5 дня)
  - Обновить API-документацию и пользовательские гайды.

</details>

### Безопасность

#### Анализ угроз и меры по их снижению

| Категория угрозы (STRIDE) | Описание угрозы в контексте Filin | Реализованные меры по минимизации |
| :--- | :--- | :--- |
| **S**poofing (Подмена) | MITM-атака на Katana | Использование HTTPS в URL. |
| **T**ampering (Фальсификация) | Изменение токена в БД | Ограничение доступа к БД, RBAC на уровне API. |
| **R**epudiation (Отказ) | Администратор отрицает добавление источника с токеном | **Логирование** всех операций создания/изменения источников. |
| **I**nformation Disclosure (Раскрытие) | **Утечка токенов** из БД или логов | **Шифрование** токенов в БД. **Маскирование** значений заголовков в логах и UI. |
| **D**enial of Service (Отказ в обслуживании) | Невалидные заголовки ломают Katana | **Валидация** имен и значений заголовков на уровне API. |
| **E**levation of Privilege (Повышение привилегий) | Инъекция в командную строку через значение заголовка | Экранирование спецсимволов при передаче аргументов в `tokio::process::Command`. |

#### Хранение и шифрование токенов

- **Хранение:** Рекомендуется хранить заголовки в виде JSON-массива в одном `TEXT` поле `http_headers`.
- **Шифрование:**
  - Чувствительные заголовки (например, `Authorization`, `Cookie`, `X-API-Key`) **должны быть зашифрованы** в базе данных.
  - Для этого можно использовать симметричное шифрование (AES-GCM) с ключом, хранящимся в защищенном месте (например, переменная окружения или Vault).
  - Расшифровка происходит непосредственно перед передачей заголовков в процесс Katana.

### Пример использования для Confluence

1.  **Получить PAT токен** в настройках профиля Confluence.
2.  **Создать источник** через UI или API:
    - **Name**: "IT-One Confluence"
    - **URL**: `https://oneproject.it-one.ru/confluence/`
    - **HTTP Headers**:
      - Header Name: `Authorization`
      - Header Value: `Bearer YOUR_PAT_TOKEN_HERE`
      - Is Sensitive: ✅ (для шифрования и маскировки)

