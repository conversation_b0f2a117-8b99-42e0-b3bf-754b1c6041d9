# Исследование: Добавление HTTP заголовков в краулер Filin

## Обзор задачи

Требуется добавить поддержку HTTP заголовков в краулер Filin для индексации закрытых ресурсов, таких как Confluence с использованием PAT (Personal Access Token) токенов.

## 1. Текущая архитектура краулера

### Краткий обзор
Filin (форк Tabby) использует внешний инструмент **Katana** для веб-краулинга. Архитектура состоит из нескольких слоев:

1. **Web UI (React)** - пользовательский интерфейс для управления источниками документов
2. **GraphQL API** - API для создания и управления веб-документами  
3. **Database Layer** - SQLite база данных для хранения конфигурации документов
4. **Background Job System** - система фоновых заданий для запуска краулинга
5. **Crawler Service** - сервис, запускающий Katana как подпроцесс

### Детальная схема потока данных

```
Web UI → GraphQL Mutation → Database → Background Job → WebCrawlerJob → tabby-crawler → Katana
```

## 2. Анализ ключевых компонентов

### 2.1 Основной краулер (`/crates/tabby-crawler/src/lib.rs`)

**Текущая реализация:**
```rust
pub async fn crawl_pipeline(start_url: &str, prefix: &str) -> Result<impl Stream<Item = CrawledDocument> + Send + Unpin> {
    // Запуск Katana без HTTP заголовков
    let mut command = tokio::process::Command::new("katana")
        .args([
            "-jc",
            "-u", start_url,
            "-fs", prefix,
            "-fx",
            "-d", "10",
            "-c", "20",
            "-p", "20",
            "-rate-limit", "5",
            "-silent",
        ])
        .stdout(Stdio::piped())
        .stderr(Stdio::piped())
        .spawn()?;
}
```

**Проблемы:**
- ❌ Нет поддержки HTTP заголовков
- ❌ Katana запускается с фиксированными аргументами
- ❌ Невозможно передать токены авторизации

### 2.2 GraphQL схема (`/ee/tabby-schema/src/schema/web_documents.rs`)

**Текущая структура:**
```rust
#[derive(InputObject)]
pub struct CreateCustomDocumentInput {
    pub name: String,
    pub url: String,
}
```

**Проблемы:**
- ❌ Отсутствует поле для HTTP заголовков
- ❌ Валидация не учитывает заголовки

### 2.3 База данных (`/ee/tabby-db/src/web_documents.rs`)

**Текущая таблица `web_documents`:**
```sql
CREATE TABLE web_documents (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    url TEXT NOT NULL,
    is_preset BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Проблемы:**
- ❌ Нет поля для хранения HTTP заголовков
- ❌ Схема БД требует миграции

### 2.4 Фоновые задания (`/ee/tabby-webserver/src/service/background_job/web_crawler.rs`)

**Текущая структура WebCrawlerJob:**
```rust
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WebCrawlerJob {
    source_id: String,
    pub url: String,
    url_prefix: Option<String>,
}
```

**Проблемы:**
- ❌ Нет поля для HTTP заголовков
- ❌ Заголовки не передаются в краулер

## 3. Возможности Katana для HTTP заголовков

Katana поддерживает HTTP заголовки через флаг `-H`:

```bash
katana -u "https://example.com" -H "Authorization: Bearer token123" -H "X-Custom-Header: value"
```

**Возможности:**
- ✅ Поддержка множественных заголовков
- ✅ Поддержка всех стандартных HTTP заголовков
- ✅ Поддержка кастомных заголовков

## 4. Confluence как целевой источник

### Аутентификация в Confluence
Confluence поддерживает несколько методов аутентификации:

1. **Basic Auth** (username:password)
2. **Bearer Token** (PAT токены)
3. **API Key** (в заголовке)

**Пример для Confluence Cloud:**
```bash
# Personal Access Token
Authorization: Bearer <PAT_TOKEN>

# Basic Auth
Authorization: Basic <base64(username:password)>
```

## 5. Предлагаемое решение

### 5.1 Изменения в базе данных

**Миграция БД:**
```sql
-- Добавить поле для хранения HTTP заголовков
ALTER TABLE web_documents ADD COLUMN http_headers TEXT;
```

**Формат хранения (JSON):**
```json
{
  "Authorization": "Bearer your-pat-token",
  "X-Custom-Header": "custom-value"
}
```

### 5.2 Изменения в GraphQL схеме

**Новый input type:**
```rust
#[derive(InputObject)]
pub struct HttpHeader {
    pub name: String,
    pub value: String,
}

#[derive(InputObject)]
pub struct CreateCustomDocumentInput {
    pub name: String,
    pub url: String,
    pub headers: Option<Vec<HttpHeader>>,
}
```

### 5.3 Изменения в WebCrawlerJob

**Обновленная структура:**
```rust
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WebCrawlerJob {
    source_id: String,
    pub url: String,
    url_prefix: Option<String>,
    pub headers: Option<HashMap<String, String>>,
}
```

### 5.4 Изменения в краулере

**Обновленная функция crawl_pipeline:**
```rust
pub async fn crawl_pipeline(
    start_url: &str, 
    prefix: &str, 
    headers: Option<&HashMap<String, String>>
) -> Result<impl Stream<Item = CrawledDocument> + Send + Unpin> {
    let mut args = vec![
        "-jc", "-u", start_url, "-fs", prefix, "-fx",
        "-d", "10", "-c", "20", "-p", "20", 
        "-rate-limit", "5", "-silent"
    ];
    
    // Добавляем HTTP заголовки
    if let Some(headers) = headers {
        for (name, value) in headers {
            args.push("-H");
            args.push(&format!("{}:{}", name, value));
        }
    }
    
    let mut command = tokio::process::Command::new("katana")
        .args(args)
        .stdout(Stdio::piped())
        .stderr(Stdio::piped())
        .spawn()?;
    // ...
}
```

## 6. Список необходимых изменений

### 6.1 База данных
- [ ] **Миграция**: Добавить поле `http_headers TEXT` в таблицу `web_documents`
- [ ] **DAO**: Обновить методы создания/обновления для работы с заголовками
- [ ] **Валидация**: Добавить валидацию JSON формата заголовков

### 6.2 GraphQL API
- [ ] **Schema**: Добавить тип `HttpHeader` и поле `headers` в `CreateCustomDocumentInput`
- [ ] **Resolver**: Обновить резолверы для обработки заголовков
- [ ] **Validation**: Добавить валидацию заголовков (имена, значения)

### 6.3 Background Jobs
- [ ] **WebCrawlerJob**: Добавить поле `headers` в структуру
- [ ] **Serialization**: Обеспечить корректную сериализацию/десериализацию
- [ ] **Job Creation**: Передавать заголовки при создании заданий

### 6.4 Crawler Service
- [ ] **API**: Обновить `crawl_pipeline` для приема заголовков
- [ ] **Katana Integration**: Добавить формирование аргументов `-H` для Katana
- [ ] **Error Handling**: Обработка ошибок авторизации

### 6.5 Web UI
- [ ] **Components**: Создать компонент для ввода HTTP заголовков
- [ ] **Forms**: Интегрировать в форму создания веб-документов
- [ ] **Validation**: Клиентская валидация заголовков

## 7. Этапы реализации

### Этап 1: База данных и модели
1. Создать миграцию для добавления поля `http_headers`
2. Обновить Rust модели для работы с заголовками
3. Добавить методы сериализации/десериализации

### Этап 2: GraphQL API
1. Обновить GraphQL схему
2. Добавить валидацию заголовков
3. Обновить резолверы

### Этап 3: Краулер
1. Обновить `crawl_pipeline` для поддержки заголовков
2. Добавить интеграцию с Katana
3. Тестирование с различными заголовками

### Этап 4: Background Jobs
1. Обновить `WebCrawlerJob`
2. Передача заголовков в краулер
3. Тестирование job системы

### Этап 5: Web UI
1. Создать компоненты для ввода заголовков
2. Интегрировать в формы
3. UX тестирование

## 8. Примеры использования

### 8.1 Confluence с PAT токеном
```json
{
  "name": "Company Confluence",
  "url": "https://company.atlassian.net/wiki/",
  "headers": [
    {
      "name": "Authorization",
      "value": "Bearer your-confluence-pat-token"
    }
  ]
}
```

### 8.2 Защищенная документация с API ключом
```json
{
  "name": "Internal API Docs",
  "url": "https://internal-docs.company.com",
  "headers": [
    {
      "name": "X-API-Key",
      "value": "your-api-key"
    },
    {
      "name": "User-Agent",
      "value": "Filin-Crawler/1.0"
    }
  ]
}
```

## 9. Безопасность и рекомендации

### 9.1 Хранение секретов
- ⚠️ **Важно**: HTTP заголовки с токенами хранятся в открытом виде в БД
- 🔐 **Рекомендация**: Рассмотреть шифрование чувствительных заголовков
- 🔒 **Альтернатива**: Интеграция с системой управления секретами

### 9.2 Валидация
- ✅ Валидировать имена заголовков (RFC 7230)
- ✅ Ограничить размер значений заголовков
- ✅ Предотвратить инъекции в командную строку

### 9.3 Логирование
- ⚠️ Не логировать значения заголовков с токенами
- ✅ Логировать только имена заголовков для отладки

## 10. Оценка сложности

### Низкая сложность ✅
- Изменения в GraphQL схеме
- Добавление полей в структуры
- Базовая интеграция с Katana

### Средняя сложность ⚠️
- Миграция базы данных
- Обновление всей цепочки передачи данных
- Web UI компоненты

### Высокая сложность ❌
- Шифрование секретов (если требуется)
- Сложная валидация заголовков
- Backward compatibility

## 11. Заключение

Добавление поддержки HTTP заголовков в краулер Filin **возможно и реализуемо**. Основные изменения требуются в 5 слоях архитектуры, но каждое изменение относительно простое.

**Время реализации**: ~1-2 недели для полной реализации
**Сложность**: Средняя (требует изменений во всей цепочке)
**Риски**: Минимальные (Katana уже поддерживает заголовки)

Это решение откроет возможность индексации защищенных ресурсов, включая Confluence, внутренние документации и API, значительно расширив возможности системы.
