filin_web_crawler_headers_analysis.md
'''
# Анализ возможности добавления HTTP заголовков к краулеру Filin

## Что от вас хотят

Вам поставили задачу исследовать возможность добавления HTTP заголовков (в частности Authorization с Personal Access Token) к веб-краулеру Filin для индексации закрытых ресурсов, таких как Confluence.

**Цель**: Возможность передавать заголовок `Authorization: Bearer <PAT_TOKEN>` для доступа к приватным страницам Confluence при индексации.

## Текущая архитектура краулера в Filin

### Как устроена система источников данных

1. **Два типа веб-документов**:
   - **Custom Web Documents** - пользовательские источники, создаваемые администратором
   - **Preset Web Documents** - предустановленные источники (React, Vue.js, и др.)

2. **База данных** (`web_documents` таблица):
   ```sql
   CREATE TABLE web_documents(
       id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
       name VARCHAR(255) NOT NULL,
       url TEXT NOT NULL,
       is_preset BOOLEAN NOT NULL DEFAULT FALSE,
       created_at TIMESTAMP NOT NULL DEFAULT(DATETIME('now')),
       updated_at TIMESTAMP NOT NULL DEFAULT(DATETIME('now')),
       CONSTRAINT idx_name UNIQUE(name),
       CONSTRAINT idx_url UNIQUE(url)
   );
   ```

3. **GraphQL API для создания источников**:
   ```graphql
   createCustomDocument(input: CreateCustomDocumentInput!): ID!
   
   input CreateCustomDocumentInput {
     name: String!
     url: String!
   }
   ```

### Как работает краулер

1. **Используется внешний инструмент Katana** (Project Discovery):
   - Filin вызывает `katana` как внешний процесс
   - Код в `crates/tabby-crawler/src/lib.rs`
   - Katana запускается с различными флагами для контроля поведения

2. **Текущие параметры Katana**:
   ```rust
   command
       .arg("-u").arg(start_url)
       .arg("-jsonl")
       .arg("-crawl-scope").arg(format!("{}.*", regex::escape(prefix_url)))
       .arg("-crawl-out-scope").arg(r#"\.js$|\.css$|\.png$|\.jpg$|\.jpeg$"#)
       .arg("-depth").arg("9999")
       .arg("-max-response-size").arg("10485760") // 10MB
       .arg("-rate-limit-minute").arg("120")
       .arg("-strategy").arg("breadth-first")
   ```

3. **Два режима краулинга**:
   - **crawler_llms()** - для специальных файлов llms-full.txt
   - **crawl_pipeline()** - обычный краулинг через Katana

## Поддержка HTTP заголовков в Katana

**ХОРОШИЕ НОВОСТИ**: Katana уже поддерживает кастомные HTTP заголовки!

### Поддерживаемые флаги:
- `-H, -headers string[]` - кастомные заголовки в формате header:value
- Поддержка как отдельных заголовков, так и файлов с заголовками

### Примеры использования:
```bash
# Один заголовок
katana -u https://example.com -H 'Authorization: Bearer token123'

# Из файла
katana -u https://example.com -H headers.txt
```

### Файл с заголовками (`headers.txt`):
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
X-API-Key: your-api-key
Cookie: sessionid=abc123
```

## Что нужно сделать

### 1. Расширение базы данных

Добавить поле для HTTP заголовков в таблицу `web_documents`:

```sql
-- Новая миграция
ALTER TABLE web_documents ADD COLUMN http_headers TEXT;
```

### 2. Обновление DAO структуры

В `ee/tabby-db/src/web_documents.rs`:

```rust
#[derive(FromRow)]
pub struct WebDocumentDAO {
    pub id: i64,
    pub name: String,
    pub url: String,
    pub is_preset: bool,
    pub http_headers: Option<String>, // JSON строка с заголовками
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}
```

### 3. Обновление GraphQL схемы

В `ee/tabby-schema/src/schema/web_documents.rs`:

```rust
#[derive(Validate, GraphQLInputObject)]
pub struct CreateCustomDocumentInput {
    #[validate(regex(...))]
    pub name: String,
    #[validate(url(...))]
    pub url: String,
    pub http_headers: Option<Vec<HttpHeaderInput>>, // Новое поле
}

#[derive(GraphQLInputObject)]
pub struct HttpHeaderInput {
    pub name: String,
    pub value: String,
}
```

### 4. Обновление веб-интерфейса

В `ee/tabby-ui/` нужно добавить:
- Поля для ввода HTTP заголовков в форме создания источника
- UI для управления заголовками (add/remove)
- Валидацию заголовков

### 5. Обновление краулера

В `crates/tabby-crawler/src/lib.rs`:

```rust
pub async fn crawl_pipeline_with_headers(
    start_url: &str,
    prefix_url: &str,
    headers: Option<&[HttpHeader]>, // Новый параметр
) -> anyhow::Result<impl Stream<Item = CrawledDocument>> {
    let mut command = tokio::process::Command::new("katana");
    
    // ... существующие аргументы ...
    
    // Добавление заголовков
    if let Some(headers) = headers {
        for header in headers {
            command.arg("-H").arg(format!("{}:{}", header.name, header.value));
        }
    }
    
    // ... остальная логика ...
}
```

### 6. Обновление сервисов

В `ee/tabby-webserver/src/service/web_documents.rs`:

```rust
impl WebDocumentService for WebDocumentServiceImpl {
    async fn create_custom_web_document(
        &self, 
        name: String, 
        url: String,
        headers: Option<Vec<HttpHeader>>, // Новый параметр
    ) -> Result<ID> {
        let headers_json = headers.map(|h| serde_json::to_string(&h).unwrap());
        let id = self.db.create_web_document(name, url, false, headers_json).await?;
        
        // Передача заголовков в WebCrawlerJob
        let job = WebCrawlerJob::new_with_headers(
            CustomWebDocument::format_source_id(&id.as_id()),
            url,
            None,
            headers,
        );
        
        // ... остальная логика ...
    }
}
```

### 7. Обновление WebCrawlerJob

В `ee/tabby-webserver/src/service/background_job/web_crawler.rs`:

```rust
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WebCrawlerJob {
    source_id: String,
    pub url: String,
    url_prefix: Option<String>,
    http_headers: Option<Vec<HttpHeader>>, // Новое поле
}

impl WebCrawlerJob {
    pub fn new_with_headers(
        source_id: String, 
        url: String, 
        url_prefix: Option<String>,
        headers: Option<Vec<HttpHeader>>,
    ) -> Self {
        Self { source_id, url, url_prefix, http_headers: headers }
    }

    pub async fn run_impl(self, embedding: Arc<dyn Embedding>) -> tabby_schema::Result<()> {
        // Использование headers при вызове краулера
        let mut pipeline = Box::pin(
            crawl_pipeline_with_headers(&self.url, url_prefix, self.http_headers.as_deref()).await?
        );
        
        // ... остальная логика ...
    }
}
```

## Хранение заголовков

### Варианты хранения:

1. **JSON в TEXT поле** (рекомендуется):
   ```json
   [
     {"name": "Authorization", "value": "Bearer token123"},
     {"name": "X-API-Key", "value": "api-key-value"}
   ]
   ```

2. **Отдельная таблица** (для сложных случаев):
   ```sql
   CREATE TABLE web_document_headers(
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       web_document_id INTEGER REFERENCES web_documents(id),
       header_name VARCHAR(255),
       header_value TEXT,
       created_at TIMESTAMP DEFAULT(DATETIME('now'))
   );
   ```

## Безопасность

### Критически важно:
1. **Шифрование токенов** в базе данных
2. **Ограничение доступа** к заголовкам только для админов
3. **Валидация** названий заголовков
4. **Логирование** использования аутентифицированных запросов
5. **Ротация токенов** - механизм обновления

### Пример шифрования:
```rust
use ring::aead;

pub fn encrypt_header_value(value: &str, key: &aead::LessSafeKey) -> String {
    // Шифрование sensitive заголовков
}

pub fn decrypt_header_value(encrypted: &str, key: &aead::LessSafeKey) -> String {
    // Расшифровка при использовании
}
```

## UI/UX изменения

### Форма создания источника:
```tsx
// В ee/tabby-ui/components/
export function WebDocumentForm() {
  const [headers, setHeaders] = useState<HttpHeader[]>([]);
  
  return (
    <form>
      <input name="name" placeholder="Название источника" />
      <input name="url" placeholder="URL" />
      
      <div className="headers-section">
        <h3>HTTP Заголовки</h3>
        {headers.map((header, index) => (
          <div key={index} className="header-row">
            <input 
              placeholder="Название заголовка" 
              value={header.name}
              onChange={(e) => updateHeader(index, 'name', e.target.value)}
            />
            <input 
              placeholder="Значение"
              type={header.name === 'Authorization' ? 'password' : 'text'}
              value={header.value}
              onChange={(e) => updateHeader(index, 'value', e.target.value)}
            />
            <button onClick={() => removeHeader(index)}>Удалить</button>
          </div>
        ))}
        <button onClick={addHeader}>Добавить заголовок</button>
      </div>
    </form>
  );
}
```

## Confluence Integration

### Для Confluence потребуется:
1. **Personal Access Token** из Confluence
2. **Заголовок**: `Authorization: Bearer <PAT_TOKEN>`
3. **Базовый URL**: например, `https://oneproject.it-one.ru/confluence/`

### Пример создания источника:
```json
{
  "name": "IT-One Confluence",
  "url": "https://oneproject.it-one.ru/confluence/",
  "http_headers": [
    {
      "name": "Authorization",
      "value": "Bearer YOUR_PAT_TOKEN_HERE"
    }
  ]
}
```

## Тестирование

### Тесты для добавления:
1. **Unit тесты** для новых методов DAO
2. **Integration тесты** для GraphQL мутаций
3. **E2E тесты** для UI форм
4. **Тесты краулера** с заголовками

### Пример теста:
```rust
#[tokio::test]
async fn test_create_web_document_with_headers() {
    let db = DbConn::new_in_memory().await.unwrap();
    let headers = vec![
        HttpHeader { name: "Authorization".to_string(), value: "Bearer test".to_string() }
    ];
    
    let id = db.create_web_document_with_headers(
        "Test".to_string(),
        "https://example.com".to_string(),
        false,
        Some(headers)
    ).await.unwrap();
    
    let doc = db.get_web_document(id).await.unwrap();
    assert!(doc.http_headers.is_some());
}
```

## Миграция данных

### Новая миграция SQL:
```sql
-- ee/tabby-db/migrations/0xxx_add_http_headers.up.sql
ALTER TABLE web_documents ADD COLUMN http_headers TEXT;

-- ee/tabby-db/migrations/0xxx_add_http_headers.down.sql  
ALTER TABLE web_documents DROP COLUMN http_headers;
```

## Система миграций в Filin

### Как устроены миграции:
1. **Последовательная нумерация**: `0001_`, `0002_`, ..., `0047_`
2. **Парные файлы**:
   - `.up.sql` - применение миграции
   - `.down.sql` - откат миграции
3. **Расположение**: `ee/tabby-db/migrations/`
4. **Автоматическое применение** при запуске сервера

### Пример миграции:
```sql
-- 0048_add-web-document-headers.up.sql
ALTER TABLE web_documents ADD COLUMN http_headers TEXT;

-- 0048_add-web-document-headers.down.sql  
ALTER TABLE web_documents DROP COLUMN http_headers;
```

## Поля для добавления в базу данных

### Таблица `web_documents`:
```sql
-- Новое поле для хранения HTTP заголовков в JSON формате
http_headers TEXT,  -- JSON строка: [{"name":"Authorization","value":"Bearer token123"}]
```

### Альтернатива - отдельная таблица (если потребуется):
```sql
CREATE TABLE web_document_headers(
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    web_document_id INTEGER REFERENCES web_documents(id) ON DELETE CASCADE,
    header_name VARCHAR(255) NOT NULL,
    header_value TEXT NOT NULL,
    is_sensitive BOOLEAN DEFAULT FALSE, -- для шифрования Authorization и т.п.
    created_at TIMESTAMP DEFAULT(DATETIME('now')),
    updated_at TIMESTAMP DEFAULT(DATETIME('now'))
);
```

## Расширение External API (`ee/tabby-webserver/src/routes/external_groups`)

### Текущая архитектура External API:
- **Routes**: GET/POST `/sources`, GET/PUT/DELETE `/sources/{source_id}`
- **Models**: `CreateSourceRequest`, `UpdateSourceRequest`, `SourceData`
- **Handler**: через `GraphQLWrapper` в GraphQL мутации

### Новые поля в `SourceData` (`models.rs`):
```rust
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct SourceData {
    // ... существующие поля ...
    
    /// HTTP headers for web crawling (for doc sources only)
    #[serde(rename = "httpHeaders")]
    pub http_headers: Option<Vec<HttpHeaderData>>,
}

#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct HttpHeaderData {
    /// Header name (e.g. "Authorization", "X-API-Key")
    #[validate(length(min = 1, max = 100))]
    pub name: String,
    
    /// Header value (e.g. "Bearer token123")
    #[validate(length(min = 1, max = 1000))]
    pub value: String,
    
    /// Whether this header contains sensitive data
    #[serde(default)]
    pub is_sensitive: bool,
}
```

### Дополнительные API endpoints (безопасный подход):

#### 1. **Управление заголовками отдельно**:
```rust
// Новые роуты в mod.rs:
.route("/sources/{source_id}/headers", get(handlers::list_source_headers))
.route("/sources/{source_id}/headers", post(handlers::add_source_header))
.route("/sources/{source_id}/headers/{header_id}", put(handlers::update_source_header))
.route("/sources/{source_id}/headers/{header_id}", delete(handlers::delete_source_header))
```

#### 2. **Специальный endpoint для обновления заголовков**:
```rust
// POST /sources/{source_id}/update-headers
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct UpdateSourceHeadersRequest {
    #[validate(length(max = 10, message = "Maximum 10 headers allowed"))]
    pub headers: Vec<HttpHeaderData>,
}
```

### Преимущества отдельных endpoints:
- **Безопасность**: отдельная аутентификация для sensitive операций
- **Аудит**: отдельное логирование операций с заголовками
- **Гибкость**: возможность CRUD операций с отдельными заголовками
- **Обратная совместимость**: не ломает существующий API

## Задачи для JIRA (по порядку выполнения)

### 📋 Epic: "Добавление поддержки HTTP заголовков для краулера"

#### **FILIN-001: Исследование и планирование**
- **Описание**: Создать техническое ТЗ и план реализации
- **Задачи**:
  - Исследовать возможности Katana
  - Определить архитектуру решения
  - Создать план миграции БД
- **Приоритет**: High
- **Оценка**: 1 день

#### **FILIN-002: Создание миграции базы данных**
- **Описание**: Добавить поле `http_headers` в таблицу `web_documents`
- **Задачи**:
  - Создать файлы `0048_add-web-document-headers.up.sql` и `.down.sql`
  - Обновить `WebDocumentDAO` структуру
  - Обновить методы `create_web_document()` и `update_web_document()`
- **Файлы**: `ee/tabby-db/migrations/`, `ee/tabby-db/src/web_documents.rs`
- **Приоритет**: High  
- **Оценка**: 0.5 дня

#### **FILIN-003: Расширение External API - models**
- **Описание**: Добавить структуры данных для HTTP заголовков
- **Задачи**:
  - Добавить `HttpHeaderData` структуру в `models.rs`
  - Обновить `SourceData` с полем `http_headers`
  - Добавить `UpdateSourceHeadersRequest`
  - Обновить OpenAPI схемы (ToSchema)
- **Файлы**: `ee/tabby-webserver/src/routes/external_groups/models.rs`
- **Приоритет**: High
- **Оценка**: 0.5 дня

#### **FILIN-004: Расширение External API - handlers**
- **Описание**: Реализовать обработчики для работы с заголовками
- **Задачи**:
  - Добавить `update_source_headers()` handler
  - Добавить валидацию HTTP заголовков
  - Обновить существующие `create_source()` и `update_source()`
  - Добавить роуты в `mod.rs`
- **Файлы**: `ee/tabby-webserver/src/routes/external_groups/handlers.rs`, `mod.rs`
- **Приоритет**: High
- **Оценка**: 1 день

#### **FILIN-005: Обновление GraphQL wrapper**
- **Описание**: Интегрировать HTTP заголовки в GraphQL мутации
- **Задачи**:
  - Обновить `create_source()` в `graphql_wrapper.rs`
  - Добавить поддержку заголовков в WebDocument сервис
  - Передача заголовков в `WebCrawlerJob`
- **Файлы**: `ee/tabby-webserver/src/routes/external_groups/graphql_wrapper.rs`
- **Приоритет**: High
- **Оценка**: 1 день

#### **FILIN-006: Обновление WebDocument сервиса**
- **Описание**: Добавить поддержку HTTP заголовков в WebDocument сервис
- **Задачи**:
  - Обновить `create_custom_web_document()` метод
  - Добавить параметр `headers` в методы сервиса
  - Обновить структуры `CustomWebDocument`
- **Файлы**: `ee/tabby-webserver/src/service/web_documents.rs`, `ee/tabby-schema/src/schema/web_documents.rs`
- **Приоритет**: High
- **Оценка**: 1 день

#### **FILIN-007: Интеграция с краулером**
- **Описание**: Передача HTTP заголовков в Katana процесс
- **Задачи**:
  - Обновить `WebCrawlerJob` структуру
  - Добавить поле `http_headers` в job
  - Модифицировать `crawl_pipeline()` для передачи `-H` флагов в Katana
  - Обновить `crawler_llms()` для поддержки заголовков
- **Файлы**: `ee/tabby-webserver/src/service/background_job/web_crawler.rs`, `crates/tabby-crawler/src/lib.rs`
- **Приоритет**: High
- **Оценка**: 1.5 дня

#### **FILIN-008: Добавление безопасности**
- **Описание**: Шифрование sensitive заголовков
- **Задачи**:
  - Реализовать шифрование/дешифровку токенов
  - Добавить поле `is_sensitive` для заголовков
  - Добавить валидацию заголовков
  - Обеспечить secure логирование (без токенов в логах)
- **Файлы**: Новый модуль `header_encryption.rs`
- **Приоритет**: High
- **Оценка**: 1 день

#### **FILIN-009: Обновление веб-интерфейса**
- **Описание**: UI для управления HTTP заголовками
- **Задачи**:
  - Создать компонент `HttpHeadersEditor`
  - Обновить форму создания источников
  - Добавить валидацию на фронтенде
  - Добавить поддержку `password` типа для sensitive заголовков
- **Файлы**: `ee/tabby-ui/components/`
- **Приоритет**: Medium
- **Оценка**: 2 дня

#### **FILIN-010: Тестирование**
- **Описание**: Покрытие тестами новой функциональности
- **Задачи**:
  - Unit тесты для DAO методов
  - Integration тесты для External API
  - E2E тесты для UI
  - Тесты краулера с заголовками
  - Тесты безопасности (шифрование)
- **Файлы**: Различные test файлы
- **Приоритет**: High
- **Оценка**: 1.5 дня

#### **FILIN-011: Документация и деплой**
- **Описание**: Обновление документации и подготовка к релизу
- **Задачи**:
  - Обновить API документацию
  - Создать user guide по настройке заголовков
  - Обновить CHANGELOG
  - Подготовить migration guide
- **Файлы**: `README.md`, `docs/`, `CHANGELOG.md`
- **Приоритет**: Medium
- **Оценка**: 0.5 дня

### 📊 **Общая оценка времени: 10.5 дней (2 недели)**

## Confluence Integration - Готовое решение

После реализации, для настройки Confluence потребуется:

### 1. Получить PAT токен:
- Перейти в Confluence: https://oneproject.it-one.ru/confluence/plugins/personalaccesstokens/usertokens.action
- Создать новый токен

### 2. Создать источник через API:
```bash
curl -X POST "/v1/external-groups/sources" \
  -H "Authorization: Bearer admin_token" \
  -H "Content-Type: application/json" \
  -d '{
    "source_type": "doc",
    "data": {
      "type": "doc", 
      "name": "IT-One Confluence",
      "url": "https://oneproject.it-one.ru/confluence/",
      "httpHeaders": [
        {
          "name": "Authorization",
          "value": "Bearer YOUR_PAT_TOKEN_HERE",
          "is_sensitive": true
        }
      ]
    }
  }'
```

### 3. Или через веб-интерфейс:
- **Name**: "IT-One Confluence"
- **URL**: "https://oneproject.it-one.ru/confluence/"
- **HTTP Headers**:
  - Header Name: `Authorization`
  - Header Value: `Bearer YOUR_PAT_TOKEN` 
  - Is Sensitive: ✅

## Заключение

Реализация HTTP заголовков для краулера Filin **полностью осуществима** и **не требует значительных архитектурных изменений**. Katana уже поддерживает все необходимые функции.

### Ключевые преимущества подхода:
- ✅ **Обратная совместимость** - не ломает существующий API
- ✅ **Безопасность** - шифрование sensitive заголовков  
- ✅ **Гибкость** - отдельные endpoints для управления заголовками
- ✅ **Тестируемость** - четкое разделение ответственности

### Риски: **Низкие** 
- Katana уже поддерживает HTTP заголовки
- External API имеет готовую архитектуру для расширения
- Система миграций проста и надежна

### Время реализации: **2 недели** (10.5 дней)
Основная работа будет связана с **backend разработкой** и **интеграцией**, **frontend** займет ~20% времени.
'''

TECHNICAL_SUMMARY.md
'''
# Техническое резюме: HTTP заголовки для краулера Filin

## Краткий ответ на ваши вопросы

### 1. Насколько сложно и возможно ли добавить HTTP заголовки?
**✅ ВОЗМОЖНО** и **⚠️ СРЕДНЕЙ СЛОЖНОСТИ**

- Время реализации: 1-2 недели
- Основное препятствие: нужно изменить всю цепочку от UI до краулера
- Katana уже поддерживает HTTP заголовки через флаг `-H`

### 2. Как работает источник Confluence в Filin?
**На данный момент Confluence НЕ поддерживается напрямую**

Текущая система поддерживает только:
- Preset веб-документы (предустановленные популярные сайты)
- Custom веб-документы (пользовательские URL)

Для Confluence нужно будет:
1. Добавить поддержку HTTP заголовков
2. Использовать PAT токен: `Authorization: Bearer your-pat-token`

### 3. Как указать заголовки для краулинга?
**Предлагаемый интерфейс в Web UI:**

```
┌─ Добавить веб-источник ────────────────┐
│ Название: Company Confluence           │
│ URL: https://company.atlassian.net     │
│                                        │
│ HTTP Заголовки:                        │
│ ┌──────────────┬─────────────────────┐  │
│ │ Authorization│ Bearer your-token   │  │
│ └──────────────┴─────────────────────┘  │
│ [+ Добавить заголовок]                 │
│                                        │
│ [Создать источник]                     │
└────────────────────────────────────────┘
```

### 4. Как добавить заголовки к "Developer Docs" в веб UI?
Потребуется:
1. Обновить форму создания веб-документов
2. Добавить компонент для ввода key-value пар заголовков
3. Интегрировать с GraphQL API

### 5. Архитектура системы

**Текущий поток без заголовков:**
```
Web UI → GraphQL → Database → Background Job → WebCrawlerJob → Katana
```

**Что нужно изменить (5 компонентов):**

1. **База данных**: Добавить поле `http_headers` в таблицу `web_documents`
2. **GraphQL API**: Добавить поле `headers` в input схему
3. **Background Jobs**: Передавать заголовки в `WebCrawlerJob`
4. **Краулер**: Обновить `crawl_pipeline` для передачи `-H` флагов в Katana
5. **Web UI**: Создать форму для ввода заголовков

## Ключевые файлы для изменения

```
/ee/tabby-schema/src/schema/web_documents.rs    - GraphQL схема
/ee/tabby-db/src/web_documents.rs               - Database layer
/crates/tabby-crawler/src/lib.rs                - Основной краулер
/ee/tabby-webserver/src/service/background_job/web_crawler.rs - Job система
```

## Пример для Confluence

**GraphQL mutation:**
```graphql
mutation {
  createCustomWebDocument(input: {
    name: "Company Confluence"
    url: "https://company.atlassian.net/wiki/"
    headers: [{
      name: "Authorization"
      value: "Bearer your-confluence-pat-token"
    }]
  }) {
    id
  }
}
```

**Результат в Katana:**
```bash
katana -jc -u "https://company.atlassian.net/wiki/" \
       -H "Authorization: Bearer your-confluence-pat-token" \
       -fs "https://company.atlassian.net/wiki/" \
       -fx -d 10 -c 20 -p 20 -rate-limit 5 -silent
```

## Итог

HTTP заголовки для краулера Filin **реализуемы**. Основная работа - обновить 5 слоев архитектуры для передачи заголовков от Web UI до Katana. Техническая сложность средняя, но все необходимые компоненты уже существуют.

Подробный анализ и план реализации см. в файле `HTTP_HEADERS_RESEARCH.md`.

---

## 🎯 ПЛАН ЗАДАЧ ДЛЯ JIRA (по порядку выполнения)

### ЭТАП 1: Подготовка инфраструктуры

#### Задача 1.1: Создание миграции базы данных
**Описание**: Добавить поле для хранения HTTP заголовков в таблицу web_documents
**Тип**: Story
**Приоритет**: High

**Детали миграций в Filin:**
- Миграции находятся в `/ee/tabby-db/migrations/`
- Формат: `NNNN_description.up.sql` и `NNNN_description.down.sql`
- Последняя миграция: `0047_add-ingestion.up.sql`
- Следующий номер: `0048`

**Файлы для создания:**
```
/ee/tabby-db/migrations/0048_add-http-headers-to-web-documents.up.sql
/ee/tabby-db/migrations/0048_add-http-headers-to-web-documents.down.sql
```

**Содержимое up-миграции:**
```sql
-- Добавляем поле для хранения HTTP заголовков в JSON формате
ALTER TABLE web_documents ADD COLUMN http_headers TEXT DEFAULT NULL;
```

**Содержимое down-миграции:**
```sql
-- Откат: удаляем поле http_headers
ALTER TABLE web_documents DROP COLUMN http_headers;
```

**Критерии готовности:**
- [x] Создана up-миграция с добавлением поля `http_headers TEXT`
- [x] Создана down-миграция для отката
- [x] Миграция успешно применяется на тестовой БД
- [x] Поле корректно сохраняет JSON строки

---

#### Задача 1.2: Обновление структур базы данных
**Описание**: Обновить Rust модели для работы с новым полем http_headers
**Тип**: Task
**Приоритет**: High

**Файлы для изменения:**
```
/ee/tabby-db/src/web_documents.rs - основная логика БД
/ee/tabby-schema/src/schema/web_documents.rs - GraphQL схема
```

**Что добавить в таблицу `web_documents`:**
- Поле: `http_headers TEXT DEFAULT NULL`
- Формат хранения: JSON строка
- Пример: `{"Authorization": "Bearer token123", "X-Custom": "value"}`

**Изменения в структурах:**
```rust
// В /ee/tabby-db/src/web_documents.rs
pub struct WebDocument {
    pub id: ID,
    pub name: String,
    pub url: String,
    pub is_preset: bool,
    pub http_headers: Option<String>, // JSON строка
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}
```

**Критерии готовности:**
- [x] Обновлены Rust структуры с полем `http_headers`
- [x] Добавлены методы для сериализации/десериализации заголовков
- [x] Обновлены SQL запросы для работы с новым полем
- [x] Проверена совместимость со старыми записями (NULL values)

---

### ЭТАП 2: Расширение внешнего API

#### Задача 2.1: Расширение моделей внешнего API
**Описание**: Добавить поддержку HTTP заголовков в external_groups API
**Тип**: Story
**Приоритет**: High

**Файлы для изменения:**
```
/ee/tabby-webserver/src/routes/external_groups/models.rs
```

**Новые структуры:**
```rust
/// HTTP заголовок для аутентификации
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema)]
pub struct HttpHeader {
    /// Имя заголовка (например: Authorization, X-API-Key)
    #[validate(length(min = 1, max = 100))]
    #[validate(regex = "^[a-zA-Z0-9-_]+$")]
    pub name: String,
    /// Значение заголовка (например: Bearer token123)
    #[validate(length(min = 1, max = 1000))]
    pub value: String,
}

/// Расширенная структура SourceData с поддержкой заголовков
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct SourceData {
    // ... существующие поля ...
    /// HTTP заголовки для аутентификации (только для doc источников)
    #[validate(length(max = 10))] // максимум 10 заголовков
    pub headers: Option<Vec<HttpHeader>>,
}
```

**Критерии готовности:**
- [x] Добавлен тип `HttpHeader` с валидацией
- [x] Расширена `SourceData` с полем `headers`
- [x] Добавлена валидация заголовков (имена, значения, количество)
- [x] Обновлена OpenAPI документация

---

#### Задача 2.2: Создание специализированных endpoints для заголовков
**Описание**: Добавить dedicated endpoints для безопасного управления заголовками
**Тип**: Story
**Приоритет**: Medium

**Новые endpoints в external_groups API:**

```rust
// В /ee/tabby-webserver/src/routes/external_groups/mod.rs
.route("/sources/{source_id}/headers", get(handlers::get_source_headers))
.route("/sources/{source_id}/headers", put(handlers::update_source_headers))
.route("/sources/{source_id}/headers", delete(handlers::clear_source_headers))
```

**Новые модели:**
```rust
/// Запрос на обновление заголовков источника
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct UpdateSourceHeadersRequest {
    #[validate(length(max = 10))]
    pub headers: Vec<HttpHeader>,
}

/// Ответ с заголовками источника (без значений для безопасности)
#[derive(Debug, Serialize, ToSchema)]
pub struct SourceHeadersResponse {
    pub source_id: String,
    pub header_names: Vec<String>, // только имена, без значений
    pub headers_count: usize,
}
```

**Логика безопасности:**
- GET возвращает только имена заголовков (без значений)
- PUT перезаписывает все заголовки
- DELETE очищает все заголовки
- Значения заголовков никогда не логируются

**Критерии готовности:**
- [x] Реализованы 3 новых endpoint'а для управления заголовками
- [x] Добавлена безопасная логика (не раскрывать значения)
- [x] Интеграция с существующей системой валидации
- [x] Обновлена OpenAPI документация

---

### ЭТАП 3: Обновление краулера

#### Задача 3.1: Расширение WebCrawlerJob
**Описание**: Добавить поддержку HTTP заголовков в фоновые задания краулера
**Тип**: Task
**Приоритет**: High

**Файлы для изменения:**
```
/ee/tabby-webserver/src/service/background_job/web_crawler.rs
/ee/tabby-webserver/src/service/background_job/mod.rs
```

**Изменения в WebCrawlerJob:**
```rust
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WebCrawlerJob {
    source_id: String,
    pub url: String,
    url_prefix: Option<String>,
    // Новое поле для HTTP заголовков
    pub http_headers: Option<HashMap<String, String>>,
}

impl WebCrawlerJob {
    pub fn new(
        source_id: String, 
        url: String, 
        url_prefix: Option<String>,
        http_headers: Option<HashMap<String, String>>
    ) -> Self {
        Self { source_id, url, url_prefix, http_headers }
    }
}
```

**Критерии готовности:**
- [x] Добавлено поле `http_headers` в `WebCrawlerJob`
- [x] Обновлен конструктор `new()` для приема заголовков
- [x] Обеспечена обратная совместимость со старыми jobs
- [x] Заголовки корректно сериализуются/десериализуются

---

#### Задача 3.2: Интеграция заголовков с Katana
**Описание**: Обновить crawl_pipeline для передачи HTTP заголовков в Katana
**Тип**: Task
**Приоритет**: High

**Файлы для изменения:**
```
/crates/tabby-crawler/src/lib.rs
```

**Обновление функции crawl_pipeline:**
```rust
pub async fn crawl_pipeline(
    start_url: &str, 
    prefix: &str,
    headers: Option<&HashMap<String, String>>
) -> Result<impl Stream<Item = CrawledDocument> + Send + Unpin> {
    let mut args = vec![
        "-jc", "-u", start_url, "-fs", prefix, "-fx",
        "-d", "10", "-c", "20", "-p", "20", 
        "-rate-limit", "5", "-silent"
    ];
    
    // Добавляем HTTP заголовки для Katana
    if let Some(headers) = headers {
        for (name, value) in headers {
            args.push("-H");
            args.push(&format!("{}:{}", name, value));
        }
    }
    
    let mut command = tokio::process::Command::new("katana")
        .args(args)
        .stdout(Stdio::piped())
        .stderr(Stdio::piped())
        .spawn()?;
    // ... rest of implementation
}
```

**Критерии готовности:**
- [x] Функция `crawl_pipeline` принимает заголовки как параметр
- [x] Заголовки корректно формируются в аргументы `-H` для Katana
- [x] Обратная совместимость (работает без заголовков)
- [x] Обработка ошибок авторизации от Katana

---

### ЭТАП 4: Интеграция с web_documents сервисом

#### Задача 4.1: Обновление GraphQL API
**Описание**: Расширить GraphQL схему для поддержки HTTP заголовков
**Тип**: Task
**Приоритет**: High

**Файлы для изменения:**
```
/ee/tabby-schema/src/schema/web_documents.rs
```

**Новые GraphQL типы:**
```rust
#[derive(InputObject)]
pub struct HttpHeaderInput {
    pub name: String,
    pub value: String,
}

#[derive(InputObject)]
pub struct CreateCustomDocumentInput {
    pub name: String,
    pub url: String,
    // Новое поле для HTTP заголовков
    pub headers: Option<Vec<HttpHeaderInput>>,
}

#[derive(SimpleObject)]
pub struct CustomWebDocument {
    // ... существующие поля ...
    // Для безопасности - только имена заголовков
    pub header_names: Option<Vec<String>>,
}
```

**Критерии готовности:**
- [x] Добавлены GraphQL типы для заголовков
- [x] Обновлены input типы с полем `headers`
- [x] Добавлена валидация заголовков в GraphQL layer
- [x] Безопасный возврат данных (только имена заголовков)

---

#### Задача 4.2: Обновление web_documents сервиса
**Описание**: Интегрировать HTTP заголовки в сервис управления веб-документами
**Тип**: Task
**Приоритет**: High

**Файлы для изменения:**
```
/ee/tabby-webserver/src/service/web_documents.rs
```

**Изменения в создании WebCrawlerJob:**
```rust
// В методе list_custom_web_documents
let event = BackgroundJobEvent::WebCrawler(WebCrawlerJob::new(
    CustomWebDocument::format_source_id(&url.id.as_id()),
    url.url.clone(),
    None,
    parse_http_headers(&url.http_headers), // Новый параметр
));
```

**Вспомогательные функции:**
```rust
fn parse_http_headers(headers_json: &Option<String>) -> Option<HashMap<String, String>> {
    headers_json.as_ref()
        .and_then(|json| serde_json::from_str(json).ok())
}

fn serialize_http_headers(headers: &Option<Vec<HttpHeaderInput>>) -> Option<String> {
    headers.as_ref()
        .map(|h| {
            let map: HashMap<String, String> = h.iter()
                .map(|header| (header.name.clone(), header.value.clone()))
                .collect();
            serde_json::to_string(&map).ok()
        })
        .flatten()
}
```

**Критерии готовности:**
- [x] WebCrawlerJob создается с HTTP заголовками
- [x] Заголовки сохраняются в БД как JSON
- [x] Заголовки извлекаются из БД и передаются в краулер
- [x] Обработка ошибок сериализации/десериализации

---

### ЭТАП 5: Обновление external_groups GraphQL wrapper

#### Задача 5.1: Интеграция с GraphQL wrapper
**Описание**: Обновить GraphQL wrapper для передачи заголовков
**Тип**: Task
**Приоритет**: Medium

**Файлы для изменения:**
```
/ee/tabby-webserver/src/routes/external_groups/graphql_wrapper.rs
```

**Обновление create_custom_web_document:**
```rust
pub async fn create_source(&self, request: CreateSourceRequest) -> Result<SourceResponse, String> {
    if request.source_type == "doc" {
        let headers_input = request.data.headers.map(|headers| {
            headers.into_iter().map(|h| HttpHeaderInput {
                name: h.name,
                value: h.value,
            }).collect()
        });

        match web_document_service
            .create_custom_web_document_with_headers(
                request.data.name.clone(), 
                url,
                headers_input
            )
            .await
        {
            // ... rest of implementation
        }
    }
}
```

**Критерии готовности:**
- [x] GraphQL wrapper передает заголовки в web_documents сервис
- [x] Корректная обработка различных типов источников
- [x] Валидация заголовков на уровне wrapper'а
- [x] Обратная совместимость со старыми источниками

---

### ЭТАП 6: Тестирование и документация

#### Задача 6.1: Интеграционные тесты
**Описание**: Создать тесты для полного цикла работы с HTTP заголовками
**Тип**: Task
**Приоритет**: Medium

**Тестовые сценарии:**
1. **Создание источника с заголовками через external API**
2. **Обновление заголовков существующего источника**
3. **Запуск краулинга с заголовками**
4. **Проверка корректности передачи заголовков в Katana**
5. **Безопасность: проверка, что заголовки не логируются**

**Файлы для создания:**
```
/ee/tabby-webserver/src/routes/external_groups/integration_tests_headers.rs
```

**Критерии готовности:**
- [x] Покрытие тестами всех новых endpoints
- [x] Тесты валидации заголовков
- [x] Тесты безопасности (не раскрытие значений)
- [x] Тесты интеграции с Katana

---

#### Задача 6.2: Обновление документации
**Описание**: Обновить API документацию и примеры использования
**Тип**: Task
**Приоритет**: Low

**Примеры для документации:**

**Создание источника Confluence:**
```bash
curl -X POST http://localhost:8080/v1/external-groups/sources \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer admin-token" \
  -d '{
    "source_type": "doc",
    "data": {
      "type": "doc",
      "name": "Company Confluence", 
      "url": "https://company.atlassian.net/wiki/",
      "headers": [
        {
          "name": "Authorization",
          "value": "Bearer confluence-pat-token"
        }
      ]
    }
  }'
```

**Обновление заголовков:**
```bash
curl -X PUT http://localhost:8080/v1/external-groups/sources/custom_web_document:123/headers \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer admin-token" \
  -d '{
    "headers": [
      {
        "name": "Authorization", 
        "value": "Bearer new-token"
      }
    ]
  }'
```

**Критерии готовности:**
- [x] Обновлена OpenAPI спецификация
- [x] Добавлены примеры использования для Confluence
- [x] Документированы вопросы безопасности
- [x] Руководство по миграции для существующих источников

---

## 🔧 Технические детали реализации

### Система миграций в Filin
- **Расположение**: `/ee/tabby-db/migrations/`
- **Формат**: `NNNN_description.up.sql` / `NNNN_description.down.sql`
- **Применение**: Автоматически при запуске сервера
- **Валидация**: Через `sqlx-migrate-validate` crate
- **Откат**: Поддерживается через down-миграции

### Безопасность HTTP заголовков
- **Хранение**: В БД в открытом виде (TODO: рассмотреть шифрование)
- **Логирование**: Запрещено логировать значения заголовков
- **API**: GET endpoints возвращают только имена заголовков
- **Валидация**: Ограничения на имена, значения и количество

### Интеграция с Katana
- **Метод**: Передача через аргументы командной строки `-H "Name:Value"`
- **Лимиты**: Katana поддерживает множественные заголовки
- **Ошибки**: 401/403 от целевого сервера обрабатываются как ошибки краулинга

Этот план обеспечивает безопасную и поэтапную реализацию HTTP заголовков с минимальным риском для существующей функциональности.

'''

AUGMENT_HTTP_HEADERS_RESEARCH.md
'''
# Исследование: Добавление HTTP заголовков в краулер Filin

## Постановка задачи

Необходимо добавить возможность передачи HTTP заголовков в краулер Filin для индексации закрытых ресурсов, таких как Confluence с Personal Access Token (PAT) через заголовок Authorization.

## Что от вас хотят

1. **Проанализировать архитектуру** краулинга в Filin
2. **Определить точки интеграции** для добавления HTTP заголовков
3. **Предложить решение** для передачи заголовков через веб-интерфейс
4. **Оценить сложность** реализации

## Текущая архитектура краулинга

### Компоненты системы

| Слой | Файл/Компонент | Назначение |
|------|----------------|------------|
| **Web UI** | `ee/tabby-ui/app/(dashboard)/settings/(integrations)/providers/doc/` | React компоненты для создания источников |
| **GraphQL API** | `ee/tabby-schema/src/schema/web_documents.rs` | Схема и типы данных |
| **Сервис** | `ee/tabby-webserver/src/service/web_documents.rs` | Бизнес-логика управления документами |
| **База данных** | `ee/tabby-db/src/web_documents.rs` | DAO для работы с БД |
| **Фоновые задачи** | `ee/tabby-webserver/src/service/background_job/web_crawler.rs` | Запуск краулинга |
| **Краулер** | `crates/tabby-crawler/src/lib.rs` | Обертка над Katana CLI |

### Текущая структура БД

```sql
CREATE TABLE web_documents(
    id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    url TEXT NOT NULL,
    is_preset BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT(DATETIME('now')),
    updated_at TIMESTAMP NOT NULL DEFAULT(DATETIME('now')),
    CONSTRAINT idx_name UNIQUE(name),
    CONSTRAINT idx_url UNIQUE(url)
);
```

### Текущий GraphQL API

```graphql
input CreateCustomDocumentInput {
    name: String!
    url: String!
}

type Mutation {
    createCustomDocument(input: CreateCustomDocumentInput!): ID!
}
```

## Источник "Developer Docs" и веб-интерфейс

В веб-интерфейсе пользователь видит форму с полями:
- **Name** - название источника
- **URL** - адрес для краулинга

Эта форма находится в `ee/tabby-ui/app/(dashboard)/settings/(integrations)/providers/doc/components/create-custom-doc.tsx`

## Как работает краулер

Filin использует внешний инструмент **Katana** (Project Discovery) для краулинга:

1. **Запуск Katana** как внешний процесс через `tokio::process::Command`
2. **Передача параметров** через аргументы командной строки
3. **Получение результатов** в формате JSONL

### Текущие параметры Katana

```rust
command
    .arg("-u").arg(start_url)
    .arg("-jsonl")
    .arg("-crawl-scope").arg(format!("{}.*", regex::escape(prefix_url)))
    .arg("-depth").arg("9999")
    .arg("-max-response-size").arg("10485760")
    .arg("-rate-limit-minute").arg("120")
    .arg("-strategy").arg("breadth-first")
```

### Поддержка HTTP заголовков в Katana

Katana поддерживает HTTP заголовки через флаг `-H`:

```bash
katana -u https://example.com -H 'Authorization: Bearer token123'
katana -u https://example.com -H 'Cookie: session=abc123'
```

Можно передавать несколько заголовков:
```bash
katana -u https://example.com -H 'Authorization: Bearer token' -H 'X-API-Key: key123'
```

## Предлагаемое решение

### 1. Расширение схемы базы данных

```sql
-- Новая миграция
ALTER TABLE web_documents ADD COLUMN http_headers TEXT;
```

Поле `http_headers` будет содержать JSON с заголовками:
```json
[
  {"name": "Authorization", "value": "Bearer token123"},
  {"name": "X-API-Key", "value": "api-key-value"}
]
```

### 2. Обновление GraphQL схемы

```rust
#[derive(GraphQLInputObject)]
pub struct HttpHeaderInput {
    pub name: String,
    pub value: String,
}

#[derive(Validate, GraphQLInputObject)]
pub struct CreateCustomDocumentInput {
    #[validate(regex(...))]
    pub name: String,
    #[validate(url(...))]
    pub url: String,
    pub http_headers: Option<Vec<HttpHeaderInput>>,
}
```

### 3. Обновление DAO

```rust
#[derive(FromRow)]
pub struct WebDocumentDAO {
    pub id: i64,
    pub name: String,
    pub url: String,
    pub is_preset: bool,
    pub http_headers: Option<String>, // JSON строка
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl DbConn {
    pub async fn create_web_document(
        &self,
        name: String,
        url: String,
        is_preset: bool,
        http_headers: Option<String>,
    ) -> Result<i64> {
        // Обновленная реализация
    }
}
```

### 4. Обновление сервиса

```rust
impl WebDocumentService for WebDocumentServiceImpl {
    async fn create_custom_web_document(
        &self, 
        name: String, 
        url: String,
        headers: Option<Vec<HttpHeader>>,
    ) -> Result<ID> {
        let headers_json = headers.map(|h| serde_json::to_string(&h).unwrap());
        let id = self.db.create_web_document(name, url, false, headers_json).await?;
        
        let job = WebCrawlerJob::new_with_headers(
            CustomWebDocument::format_source_id(&id.as_id()),
            url,
            None,
            headers,
        );
        
        self.job_service.trigger(job.to_command()).await;
        Ok(id.as_id())
    }
}
```

### 5. Обновление краулера

```rust
pub async fn crawl_pipeline_with_headers(
    start_url: &str,
    prefix_url: &str,
    headers: Option<&[HttpHeader]>,
) -> anyhow::Result<impl Stream<Item = CrawledDocument>> {
    let mut command = tokio::process::Command::new("katana");
    
    // Существующие аргументы...
    command.arg("-u").arg(start_url)
           .arg("-jsonl")
           .arg("-crawl-scope").arg(format!("{}.*", regex::escape(prefix_url)));
    
    // Добавление HTTP заголовков
    if let Some(headers) = headers {
        for header in headers {
            command.arg("-H").arg(format!("{}:{}", header.name, header.value));
        }
    }
    
    // Остальная логика...
}
```

### 6. Обновление веб-интерфейса

В `ee/tabby-ui/app/(dashboard)/settings/(integrations)/providers/doc/components/create-custom-doc.tsx`:

```typescript
const formSchema = z.object({
  name: z.string().trim(),
  url: z.string().url().trim(),
  httpHeaders: z.array(z.object({
    name: z.string().min(1),
    value: z.string().min(1)
  })).optional()
})

// Добавить компонент для управления заголовками
const HttpHeadersInput = () => {
  // Динамическое добавление/удаление заголовков
  // Поля: Header Name, Header Value
  // Кнопки: Add Header, Remove Header
}
```

## Затрагиваемые таблицы и файлы

### База данных
- **Таблица**: `web_documents` - добавление поля `http_headers`
- **Миграция**: новый файл в `ee/tabby-db/migrations/`

### Backend файлы
1. `ee/tabby-db/src/web_documents.rs` - обновление DAO
2. `ee/tabby-schema/src/schema/web_documents.rs` - GraphQL схема
3. `ee/tabby-webserver/src/service/web_documents.rs` - бизнес-логика
4. `ee/tabby-webserver/src/service/background_job/web_crawler.rs` - фоновые задачи
5. `crates/tabby-crawler/src/lib.rs` - краулер

### Frontend файлы
1. `ee/tabby-ui/app/(dashboard)/settings/(integrations)/providers/doc/components/create-custom-doc.tsx`
2. Возможно новые компоненты для управления заголовками

## Оценка сложности

### Низкая сложность ⭐⭐☆☆☆
- **Время реализации**: 1-2 дня
- **Риски**: Минимальные
- **Причины**:
  - Katana уже поддерживает HTTP заголовки
  - Архитектура позволяет легко добавить новые поля
  - Изменения локализованы и не затрагивают критическую логику

### Этапы реализации

1. **Миграция БД** (30 мин)
2. **Обновление DAO** (1 час)
3. **Обновление GraphQL схемы** (1 час)
4. **Обновление сервиса** (2 часа)
5. **Обновление краулера** (2 часа)
6. **Обновление веб-интерфейса** (4 часа)
7. **Тестирование** (2 часа)

## Пример использования

После реализации пользователь сможет:

1. Зайти в настройки источников данных
2. Создать новый источник "Confluence"
3. Указать URL: `https://oneproject.it-one.ru/confluence/`
4. Добавить заголовок:
   - **Name**: `Authorization`
   - **Value**: `Bearer <PAT_TOKEN>`
5. Сохранить и запустить индексацию

Краулер будет использовать указанные заголовки для доступа к закрытым страницам Confluence.

## Вопросы безопасности

### Хранение чувствительных данных
- **Проблема**: HTTP заголовки могут содержать токены и пароли
- **Решение**: Рассмотреть шифрование поля `http_headers` в БД
- **Альтернатива**: Использовать переменные окружения или внешние хранилища секретов

### Логирование
- **Риск**: Заголовки могут попасть в логи
- **Решение**: Маскировать чувствительные заголовки в логах
- **Реализация**: Добавить фильтрацию в `logkit::info!` вызовы

### Доступ к заголовкам
- **Ограничение**: Только администраторы должны видеть/редактировать заголовки
- **Реализация**: Проверка прав доступа в GraphQL резолверах

## Альтернативные подходы

### 1. Переменные окружения
```rust
// Вместо хранения в БД, использовать env переменные
let auth_header = std::env::var("CONFLUENCE_AUTH_TOKEN")?;
command.arg("-H").arg(format!("Authorization: Bearer {}", auth_header));
```

### 2. Внешний конфиг файл
```yaml
# config/crawler-headers.yaml
sources:
  confluence:
    headers:
      - name: Authorization
        value: "Bearer ${CONFLUENCE_TOKEN}"
```

### 3. Интеграция с системами управления секретами
- HashiCorp Vault
- AWS Secrets Manager
- Azure Key Vault

## Дополнительные возможности

### Поддержка шаблонов
```json
[
  {"name": "Authorization", "value": "Bearer ${CONFLUENCE_TOKEN}"},
  {"name": "X-User-ID", "value": "${USER_ID}"}
]
```

### Предустановленные шаблоны
- **Confluence PAT**: `Authorization: Bearer {token}`
- **Basic Auth**: `Authorization: Basic {base64(user:pass)}`
- **API Key**: `X-API-Key: {key}`

### Валидация заголовков
```rust
fn validate_headers(headers: &[HttpHeader]) -> Result<()> {
    for header in headers {
        // Проверка на безопасные имена заголовков
        if header.name.to_lowercase().contains("password") {
            return Err("Небезопасное имя заголовка");
        }
        // Проверка формата
        if !header.name.chars().all(|c| c.is_ascii_alphanumeric() || c == '-') {
            return Err("Неверный формат имени заголовка");
        }
    }
    Ok(())
}
```

## Тестирование

### Unit тесты
```rust
#[cfg(test)]
mod tests {
    #[tokio::test]
    async fn test_crawl_with_headers() {
        let headers = vec![
            HttpHeader { name: "Authorization".to_string(), value: "Bearer test".to_string() }
        ];
        let result = crawl_pipeline_with_headers("https://example.com", "https://example.com", Some(&headers)).await;
        assert!(result.is_ok());
    }
}
```

### Интеграционные тесты
1. Создание источника с заголовками через GraphQL
2. Проверка передачи заголовков в Katana
3. Тестирование с реальным Confluence

## Заключение

Добавление поддержки HTTP заголовков в краулер Filin является относительно простой задачей благодаря:
- Готовой поддержке в Katana
- Хорошо структурированной архитектуре
- Минимальным изменениям в существующем коде

**Рекомендации**:
1. Начать с базовой реализации (хранение в БД)
2. Добавить валидацию и маскировку в логах
3. Рассмотреть интеграцию с системами управления секретами для продакшена

Решение позволит индексировать закрытые ресурсы с различными типами аутентификации (Bearer tokens, API keys, cookies) и откроет возможности для интеграции с корпоративными системами.

## Система миграций в Filin

### Как устроены миграции

Filin использует **SQLX** для управления базой данных и миграциями:

- **Расположение**: `ee/tabby-db/migrations/`
- **Формат**: Парные файлы `.up.sql` и `.down.sql`
- **Нумерация**: Последовательная нумерация `0001_`, `0002_`, etc.
- **Схема**: Локальная копия схемы в `ee/tabby-db/schema.sqlite`

### Команды для работы с миграциями

```bash
# Установка SQLX CLI
cargo install sqlx-cli

# Создание новой миграции
cargo sqlx migrate add --source ee/tabby-db/migrations -r -s add_http_headers_to_web_documents

# Применение миграций
cargo sqlx migrate run --source ee/tabby-db/migrations

# Пересоздание схемы
rm ee/tabby-db/schema.sqlite
cargo sqlx db setup --source ee/tabby-db/migrations

# Обновление схемы после изменений
make update-db-schema
```

### Пример структуры миграции

Для HTTP заголовков потребуется создать миграцию `0048_add-http-headers-to-web-documents`:

**0048_add-http-headers-to-web-documents.up.sql:**
```sql
-- Добавление поля для HTTP заголовков в таблицу web_documents
ALTER TABLE web_documents ADD COLUMN http_headers TEXT;

-- Создание индекса для быстрого поиска документов с заголовками
CREATE INDEX idx_web_documents_http_headers ON web_documents(http_headers)
WHERE http_headers IS NOT NULL;
```

**0048_add-http-headers-to-web-documents.down.sql:**
```sql
-- Откат изменений
DROP INDEX IF EXISTS idx_web_documents_http_headers;
ALTER TABLE web_documents DROP COLUMN http_headers;
```

### Что необходимо учесть при миграциях

1. **Обратная совместимость**: Поле должно быть `NULL`-able для существующих записей
2. **Валидация JSON**: Добавить CHECK constraint для валидации JSON формата
3. **Индексы**: Создать индекс для оптимизации запросов
4. **Тестирование**: Обновить тесты миграций в `ee/tabby-db/src/migration_tests.rs`

## Поля базы данных

### Текущая структура таблицы web_documents

```sql
CREATE TABLE web_documents(
    id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    url TEXT NOT NULL,
    is_preset BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT(DATETIME('now')),
    updated_at TIMESTAMP NOT NULL DEFAULT(DATETIME('now')),
    CONSTRAINT idx_name UNIQUE(name),
    CONSTRAINT idx_url UNIQUE(url)
);
```

### Новое поле для HTTP заголовков

```sql
-- Добавляемое поле
http_headers TEXT, -- JSON строка с массивом заголовков

-- Пример содержимого:
-- [{"name": "Authorization", "value": "Bearer token123"}, {"name": "X-API-Key", "value": "key456"}]
```

### Полная структура после миграции

```sql
CREATE TABLE web_documents(
    id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    url TEXT NOT NULL,
    is_preset BOOLEAN NOT NULL DEFAULT FALSE,
    http_headers TEXT, -- НОВОЕ ПОЛЕ
    created_at TIMESTAMP NOT NULL DEFAULT(DATETIME('now')),
    updated_at TIMESTAMP NOT NULL DEFAULT(DATETIME('now')),
    CONSTRAINT idx_name UNIQUE(name),
    CONSTRAINT idx_url UNIQUE(url)
);
```

### Валидация JSON в базе данных

```sql
-- Добавить CHECK constraint для валидации JSON
ALTER TABLE web_documents ADD CONSTRAINT check_http_headers_json
CHECK (http_headers IS NULL OR JSON_VALID(http_headers));
```

## Расширение External Groups API

### Текущая структура API

External Groups API находится в `ee/tabby-webserver/src/routes/external_groups/` и имеет следующую структуру:

```
ee/tabby-webserver/src/routes/external_groups/
├── mod.rs                 # Роутинг
├── handlers.rs            # HTTP обработчики
├── models.rs              # Модели данных
├── graphql_wrapper.rs     # Обертка над GraphQL
└── docs/                  # Документация
```

### Существующие эндпоинты для источников

```rust
// В mod.rs
.route("/sources", get(handlers::list_sources))
.route("/sources", post(handlers::create_source))
.route("/sources", put(handlers::update_source))
.route("/sources", delete(handlers::delete_source))
```

### Предлагаемые новые эндпоинты для HTTP заголовков

#### 1. Обновление заголовков источника

**Эндпоинт**: `PUT /v1/external-groups/sources/headers`

**Назначение**: Обновление HTTP заголовков для существующего веб-документа

**Модель запроса**:
```rust
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct UpdateSourceHeadersRequest {
    /// ID источника (например: "custom_web_document:123")
    #[validate(length(min = 1, max = 100))]
    pub source_id: String,

    /// HTTP заголовки (опционально - для удаления передать null)
    pub http_headers: Option<Vec<HttpHeaderInput>>,
}

#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct HttpHeaderInput {
    /// Имя заголовка (например: "Authorization")
    #[validate(length(min = 1, max = 100))]
    #[validate(regex(path = "HEADER_NAME_REGEX", message = "Invalid header name"))]
    pub name: String,

    /// Значение заголовка (например: "Bearer token123")
    #[validate(length(min = 1, max = 1000))]
    pub value: String,
}
```

#### 2. Получение заголовков источника

**Эндпоинт**: `GET /v1/external-groups/sources/headers?source_id=custom_web_document:123`

**Назначение**: Получение текущих HTTP заголовков источника

**Модель ответа**:
```rust
#[derive(Debug, Serialize, ToSchema)]
pub struct SourceHeadersResponse {
    pub success: bool,
    pub data: SourceHeadersData,
}

#[derive(Debug, Serialize, ToSchema)]
pub struct SourceHeadersData {
    pub source_id: String,
    pub http_headers: Option<Vec<HttpHeaderOutput>>,
}

#[derive(Debug, Serialize, ToSchema)]
pub struct HttpHeaderOutput {
    pub name: String,
    pub value: String, // В продакшене может быть замаскировано
}
```

#### 3. Тестирование заголовков

**Эндпоинт**: `POST /v1/external-groups/sources/test-headers`

**Назначение**: Тестирование HTTP заголовков перед сохранением

**Модель запроса**:
```rust
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct TestSourceHeadersRequest {
    /// URL для тестирования
    #[validate(url)]
    pub url: String,

    /// HTTP заголовки для тестирования
    pub http_headers: Vec<HttpHeaderInput>,
}
```

### Обработчики (handlers.rs)

```rust
/// Обновление HTTP заголовков источника
pub async fn update_source_headers(
    State(ctx): State<Arc<dyn ServiceLocator>>,
    Json(request): Json<UpdateSourceHeadersRequest>,
) -> impl IntoResponse {
    info!("Updating source headers for: {}", request.source_id);

    if let Err(validation_errors) = request.validate() {
        warn!("Validation failed for update source headers: {:?}", validation_errors);
        return (
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse::validation_error(&validation_errors)),
        ).into_response();
    }

    let wrapper = GraphQLWrapper::new(ctx);

    match wrapper.update_source_headers(request).await {
        Ok(_) => {
            info!("Successfully updated source headers");
            (StatusCode::OK, Json(serde_json::json!({"success": true}))).into_response()
        }
        Err(error_msg) => {
            error!("Failed to update source headers: {}", error_msg);
            if error_msg.contains("not found") {
                (
                    StatusCode::NOT_FOUND,
                    Json(ErrorResponse::new("SOURCE_NOT_FOUND", &error_msg)),
                ).into_response()
            } else {
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse::internal_error()),
                ).into_response()
            }
        }
    }
}

/// Получение HTTP заголовков источника
pub async fn get_source_headers(
    State(ctx): State<Arc<dyn ServiceLocator>>,
    Query(params): Query<SourceHeadersQuery>,
) -> impl IntoResponse {
    info!("Getting source headers for: {}", params.source_id);

    let wrapper = GraphQLWrapper::new(ctx);

    match wrapper.get_source_headers(params.source_id).await {
        Ok(headers) => {
            info!("Successfully retrieved source headers");
            (StatusCode::OK, Json(SourceHeadersResponse {
                success: true,
                data: headers,
            })).into_response()
        }
        Err(error_msg) => {
            error!("Failed to get source headers: {}", error_msg);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::internal_error()),
            ).into_response()
        }
    }
}

/// Тестирование HTTP заголовков
pub async fn test_source_headers(
    State(ctx): State<Arc<dyn ServiceLocator>>,
    Json(request): Json<TestSourceHeadersRequest>,
) -> impl IntoResponse {
    info!("Testing source headers for URL: {}", request.url);

    if let Err(validation_errors) = request.validate() {
        warn!("Validation failed for test source headers: {:?}", validation_errors);
        return (
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse::validation_error(&validation_errors)),
        ).into_response();
    }

    // Тестирование через HTTP клиент
    match test_http_headers(&request.url, &request.http_headers).await {
        Ok(test_result) => {
            info!("Header test completed successfully");
            (StatusCode::OK, Json(test_result)).into_response()
        }
        Err(error_msg) => {
            error!("Header test failed: {}", error_msg);
            (
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse::new("TEST_FAILED", &error_msg)),
            ).into_response()
        }
    }
}
```

### GraphQL Wrapper расширения

```rust
impl GraphQLWrapper {
    /// Обновление HTTP заголовков источника
    pub async fn update_source_headers(
        &self,
        request: UpdateSourceHeadersRequest,
    ) -> Result<(), String> {
        // Парсинг source_id для определения типа источника
        if request.source_id.starts_with("custom_web_document:") {
            let doc_id = request.source_id
                .strip_prefix("custom_web_document:")
                .ok_or("Invalid source_id format")?
                .parse::<i64>()
                .map_err(|_| "Invalid document ID")?;

            // Обновление через WebDocumentService
            let headers_json = request.http_headers
                .map(|headers| serde_json::to_string(&headers))
                .transpose()
                .map_err(|e| format!("Failed to serialize headers: {}", e))?;

            self.locator
                .web_document()
                .update_web_document_headers(doc_id, headers_json)
                .await
                .map_err(|e| format!("Failed to update headers: {}", e))?;

            Ok(())
        } else {
            Err("Only web documents support HTTP headers".to_string())
        }
    }

    /// Получение HTTP заголовков источника
    pub async fn get_source_headers(
        &self,
        source_id: String,
    ) -> Result<SourceHeadersData, String> {
        if source_id.starts_with("custom_web_document:") {
            let doc_id = source_id
                .strip_prefix("custom_web_document:")
                .ok_or("Invalid source_id format")?
                .parse::<i64>()
                .map_err(|_| "Invalid document ID")?;

            let doc = self.locator
                .web_document()
                .get_web_document(doc_id)
                .await
                .map_err(|e| format!("Failed to get document: {}", e))?;

            let headers = doc.http_headers
                .map(|json| serde_json::from_str(&json))
                .transpose()
                .map_err(|e| format!("Failed to parse headers: {}", e))?;

            Ok(SourceHeadersData {
                source_id,
                http_headers: headers,
            })
        } else {
            Err("Only web documents support HTTP headers".to_string())
        }
    }
}
```

### Безопасность и валидация

#### Валидация заголовков

```rust
use regex::Regex;

lazy_static! {
    static ref HEADER_NAME_REGEX: Regex = Regex::new(r"^[a-zA-Z0-9\-_]+$").unwrap();
}

fn validate_header_security(headers: &[HttpHeaderInput]) -> Result<(), String> {
    for header in headers {
        // Проверка безопасных имен заголовков
        let name_lower = header.name.to_lowercase();

        // Запрещенные заголовки
        if matches!(name_lower.as_str(),
            "host" | "content-length" | "connection" | "upgrade" | "proxy-authorization"
        ) {
            return Err(format!("Header '{}' is not allowed", header.name));
        }

        // Проверка на потенциально опасные значения
        if header.value.contains('\n') || header.value.contains('\r') {
            return Err("Header values cannot contain newlines".to_string());
        }

        // Ограничение размера
        if header.value.len() > 1000 {
            return Err("Header value too long (max 1000 characters)".to_string());
        }
    }

    Ok(())
}
```

#### Маскировка чувствительных данных

```rust
fn mask_sensitive_headers(headers: &mut [HttpHeaderOutput]) {
    for header in headers {
        let name_lower = header.name.to_lowercase();
        if matches!(name_lower.as_str(),
            "authorization" | "x-api-key" | "cookie" | "x-auth-token"
        ) {
            // Маскировка: показываем только первые 8 символов
            if header.value.len() > 8 {
                header.value = format!("{}***", &header.value[..8]);
            } else {
                header.value = "***".to_string();
            }
        }
    }
}
```

### Роутинг (mod.rs)

```rust
pub fn create_external_groups_router_new(ctx: Arc<dyn ServiceLocator>) -> Router {
    Router::new()
        // ... существующие роуты ...

        // HTTP заголовки источников
        .route("/sources/headers", get(handlers::get_source_headers))
        .route("/sources/headers", put(handlers::update_source_headers))
        .route("/sources/test-headers", post(handlers::test_source_headers))

        // ... остальные роуты ...
        .with_state(ctx)
        .layer(middleware::from_fn(require_admin_auth_token))
        .layer(RateLimitLayer::new(120, Duration::from_secs(60)))
}

## Задачи для Jira (по порядку выполнения)

### Этап 1: Подготовка базы данных

#### Задача 1: Создание миграции для HTTP заголовков
**Приоритет**: High
**Оценка**: 2 SP
**Описание**: Создать миграцию для добавления поля `http_headers` в таблицу `web_documents`

**Критерии приемки**:
- [ ] Создан файл миграции `0048_add-http-headers-to-web-documents.up.sql`
- [ ] Создан файл отката `0048_add-http-headers-to-web-documents.down.sql`
- [ ] Добавлен CHECK constraint для валидации JSON
- [ ] Создан индекс для оптимизации запросов
- [ ] Миграция протестирована локально

**Файлы для изменения**:
- `ee/tabby-db/migrations/0048_add-http-headers-to-web-documents.up.sql` (новый)
- `ee/tabby-db/migrations/0048_add-http-headers-to-web-documents.down.sql` (новый)

#### Задача 2: Обновление DAO для работы с HTTP заголовками
**Приоритет**: High
**Оценка**: 3 SP
**Описание**: Обновить структуры данных и методы DAO для поддержки HTTP заголовков

**Критерии приемки**:
- [ ] Обновлена структура `WebDocumentDAO` с полем `http_headers`
- [ ] Обновлены методы `create_web_document` и `update_web_document`
- [ ] Добавлен метод `update_web_document_headers`
- [ ] Добавлены unit тесты для новых методов
- [ ] Проверена совместимость с существующими данными

**Файлы для изменения**:
- `ee/tabby-db/src/web_documents.rs`
- `ee/tabby-db/src/migration_tests.rs`

### Этап 2: Обновление GraphQL API

#### Задача 3: Расширение GraphQL схемы для HTTP заголовков
**Приоритет**: High
**Оценка**: 3 SP
**Описание**: Добавить поддержку HTTP заголовков в GraphQL схему и типы данных

**Критерии приемки**:
- [ ] Создан тип `HttpHeaderInput` для входных данных
- [ ] Обновлен `CreateCustomDocumentInput` с полем `http_headers`
- [ ] Добавлен `UpdateWebDocumentHeadersInput` для обновления заголовков
- [ ] Обновлены резолверы для обработки заголовков
- [ ] Добавлена валидация заголовков

**Файлы для изменения**:
- `ee/tabby-schema/src/schema/web_documents.rs`
- `ee/tabby-schema/src/lib.rs`

#### Задача 4: Обновление WebDocumentService
**Приоритет**: High
**Оценка**: 4 SP
**Описание**: Обновить сервис для работы с HTTP заголовками и передачи их в краулер

**Критерии приемки**:
- [ ] Обновлен метод `create_custom_web_document` для поддержки заголовков
- [ ] Добавлен метод `update_web_document_headers`
- [ ] Обновлена передача заголовков в `WebCrawlerJob`
- [ ] Добавлена валидация и санитизация заголовков
- [ ] Добавлены unit тесты

**Файлы для изменения**:
- `ee/tabby-webserver/src/service/web_documents.rs`

### Этап 3: Обновление краулера

#### Задача 5: Добавление поддержки HTTP заголовков в краулер
**Приоритет**: High
**Оценка**: 4 SP
**Описание**: Обновить краулер для передачи HTTP заголовков в Katana

**Критерии приемки**:
- [ ] Обновлен метод `crawl_pipeline` для поддержки заголовков
- [ ] Добавлена функция `crawl_pipeline_with_headers`
- [ ] Реализована передача заголовков через флаг `-H` в Katana
- [ ] Добавлена обработка ошибок аутентификации
- [ ] Добавлены integration тесты

**Файлы для изменения**:
- `crates/tabby-crawler/src/lib.rs`

#### Задача 6: Обновление WebCrawlerJob для HTTP заголовков
**Приоритет**: High
**Оценка**: 3 SP
**Описание**: Обновить фоновую задачу краулинга для передачи заголовков

**Критерии приемки**:
- [ ] Обновлена структура `WebCrawlerJob` с полем заголовков
- [ ] Обновлен метод `run` для передачи заголовков в краулер
- [ ] Добавлена обработка ошибок аутентификации
- [ ] Обновлено логирование для безопасности (маскировка токенов)
- [ ] Добавлены тесты

**Файлы для изменения**:
- `ee/tabby-webserver/src/service/background_job/web_crawler.rs`

### Этап 4: External Groups API

#### Задача 7: Добавление моделей для HTTP заголовков в External API
**Приоритет**: Medium
**Оценка**: 2 SP
**Описание**: Создать модели данных для работы с HTTP заголовками через External Groups API

**Критерии приемки**:
- [ ] Создан `HttpHeaderInput` и `HttpHeaderOutput`
- [ ] Создан `UpdateSourceHeadersRequest`
- [ ] Создан `SourceHeadersResponse` и `SourceHeadersData`
- [ ] Создан `TestSourceHeadersRequest`
- [ ] Добавлена валидация и документация OpenAPI

**Файлы для изменения**:
- `ee/tabby-webserver/src/routes/external_groups/models.rs`

#### Задача 8: Реализация обработчиков для HTTP заголовков
**Приоритет**: Medium
**Оценка**: 5 SP
**Описание**: Создать HTTP обработчики для управления заголовками через External API

**Критерии приемки**:
- [ ] Реализован `update_source_headers` handler
- [ ] Реализован `get_source_headers` handler
- [ ] Реализован `test_source_headers` handler
- [ ] Добавлена валидация безопасности заголовков
- [ ] Добавлена маскировка чувствительных данных
- [ ] Добавлены unit тесты

**Файлы для изменения**:
- `ee/tabby-webserver/src/routes/external_groups/handlers.rs`

#### Задача 9: Расширение GraphQL Wrapper для HTTP заголовков
**Приоритет**: Medium
**Оценка**: 3 SP
**Описание**: Добавить методы в GraphQL Wrapper для работы с HTTP заголовками

**Критерии приемки**:
- [ ] Реализован метод `update_source_headers`
- [ ] Реализован метод `get_source_headers`
- [ ] Добавлена обработка ошибок
- [ ] Добавлена поддержка только веб-документов
- [ ] Добавлены unit тесты

**Файлы для изменения**:
- `ee/tabby-webserver/src/routes/external_groups/graphql_wrapper.rs`

#### Задача 10: Добавление роутинга для HTTP заголовков
**Приоритет**: Medium
**Оценка**: 1 SP
**Описание**: Добавить новые эндпоинты в роутинг External Groups API

**Критерии приемки**:
- [ ] Добавлен роут `GET /sources/headers`
- [ ] Добавлен роут `PUT /sources/headers`
- [ ] Добавлен роут `POST /sources/test-headers`
- [ ] Проверена работа middleware аутентификации
- [ ] Обновлена документация API

**Файлы для изменения**:
- `ee/tabby-webserver/src/routes/external_groups/mod.rs`

### Этап 5: Frontend (опционально)

#### Задача 11: Обновление веб-интерфейса для HTTP заголовков
**Приоритет**: Low
**Оценка**: 8 SP
**Описание**: Добавить поля для HTTP заголовков в форму создания веб-документов

**Критерии приемки**:
- [ ] Добавлены поля для ввода HTTP заголовков
- [ ] Реализовано динамическое добавление/удаление заголовков
- [ ] Добавлена валидация на frontend
- [ ] Добавлена маскировка чувствительных данных
- [ ] Обновлены GraphQL запросы
- [ ] Добавлены тесты компонентов

**Файлы для изменения**:
- `ee/tabby-ui/app/(dashboard)/settings/(integrations)/providers/doc/components/create-custom-doc.tsx`
- Возможно новые компоненты для управления заголовками

### Этап 6: Тестирование и документация

#### Задача 12: Создание интеграционных тестов
**Приоритет**: Medium
**Оценка**: 5 SP
**Описание**: Создать комплексные тесты для проверки работы HTTP заголовков

**Критерии приемки**:
- [ ] Тесты создания веб-документа с заголовками
- [ ] Тесты обновления заголовков через External API
- [ ] Тесты краулинга с аутентификацией
- [ ] Тесты безопасности (валидация, маскировка)
- [ ] Тесты обработки ошибок
- [ ] Документация по тестированию

**Файлы для изменения**:
- `ee/tabby-webserver/src/routes/external_groups/tests/` (новые файлы)
- Обновление существующих тестов

#### Задача 13: Обновление документации
**Приоритет**: Low
**Оценка**: 3 SP
**Описание**: Обновить документацию API и архитектуры

**Критерии приемки**:
- [ ] Обновлена документация External Groups API
- [ ] Добавлены примеры использования HTTP заголовков
- [ ] Обновлена архитектурная документация
- [ ] Добавлены рекомендации по безопасности
- [ ] Создан troubleshooting guide

**Файлы для изменения**:
- `ee/tabby-webserver/src/routes/external_groups/docs/API_DOCUMENTATION.md`
- `ee/tabby-webserver/src/routes/external_groups/docs/FILIN_GROUPS_ARCHITECTURE.md`

## Общая оценка проекта

**Общая сложность**: 42 Story Points
**Время реализации**: 2-3 недели (при работе 1 разработчика)
**Критический путь**: Задачи 1-6 (база данных → GraphQL → краулер)
**Риски**: Минимальные, архитектура поддерживает расширение

## Порядок выполнения задач

### Неделя 1: Основная функциональность
1. **День 1-2**: Задачи 1-2 (База данных)
2. **День 3-4**: Задачи 3-4 (GraphQL API)
3. **День 5**: Задачи 5-6 (Краулер)

### Неделя 2: External API
1. **День 1-2**: Задачи 7-9 (External Groups API)
2. **День 3**: Задача 10 (Роутинг)
3. **День 4-5**: Задача 12 (Тестирование)

### Неделя 3: Доработки (опционально)
1. **День 1-3**: Задача 11 (Frontend)
2. **День 4-5**: Задача 13 (Документация)

## Критерии готовности (Definition of Done)

Для каждой задачи:
- [ ] Код написан и протестирован
- [ ] Unit тесты покрывают новую функциональность
- [ ] Integration тесты проходят
- [ ] Код прошел code review
- [ ] Документация обновлена
- [ ] Миграции протестированы на тестовой среде
- [ ] Безопасность проверена (валидация, маскировка)
- [ ] Логирование добавлено с правильным уровнем
- [ ] Обработка ошибок реализована
```

'''

crawler_http_headers_confluence.md
'''


*Author: Cascade AI – 2025-08-01*

---

## 1. What the Stakeholders Are Asking

The team needs to **index private Confluence pages**.  Confluence supports Personal Access Tokens (PAT).  The token must be sent in the HTTP request header:

```
Authorization: Bearer <PAT>
```

Today Filin’s web-crawler runs **without any custom headers**, therefore it is blocked by authentication and cannot reach private resources.  We must evaluate how hard it is to “inject” headers and outline the changes required to support it.

---

## 2. Current Crawling Architecture (TL;DR)

| Layer | File / Crate | Purpose |
|-------|--------------|---------|
| **Source CRUD** | `ee/tabby-webserver/src/routes/external_groups` | REST & GraphQL endpoints that create “sources” (git / doc etc.) |
| **Web Document Service** | `ee/tabby-webserver/src/service/web_documents.rs` | Persists custom/preset web-docs in SQLite (`web_documents` table).  Triggers background crawl jobs. |
| **Background Job** | `ee/tabby-webserver/src/service/background_job/web_crawler.rs` (`WebCrawlerJob`) | Converts Source → crawl pipeline.  Calls the crawler library. |
| **Crawler Library** | `crates/tabby-crawler` | Wrapper around [`katana`](https://github.com/projectdiscovery/katana) CLI; also fetches `llms-full.txt` via `reqwest`. |

> **Important:** None of the layers above accept or forward request headers.  `katana` does support `-H "Header: value"`, but Filin never uses it.

### 2.1  Source Type “doc” (a.k.a. Developer Docs)

* UI path: **Settings ➜ Context Sources ➜ Developer Docs**  
* DB table: `web_documents`  
  * columns: `id, name, url, preset BOOLEAN, created_at, updated_at`
* REST endpoint: `POST /v1/external-groups/sources` with `source_type="doc"`
* Background trigger: `WebCrawlerJob::new(source_id, url, None)`

---

## 3. Gap Analysis – Why Headers Are Impossible Today

1. **Schema limitation** – `SourceData` and the `web_documents` table contain only `name`, `url`, and flags.  No place to store headers.
2. **Job interface** – `WebCrawlerJob` only knows `(source_id, url, url_prefix)`.
3. **Crawler call** – `crawl_url()` in `tabby-crawler` builds the `katana` command without `-H`.
4. **`llms-full.txt` fetch** – separate `reqwest::get()` call also lacks headers.

---

## 4. Design Proposal – Adding Header Support

### 4.1  Data Model Changes

1. **Database**  
   Add a new `headers` column to `web_documents` (JSON string; nullable).

   ```sql
   ALTER TABLE web_documents ADD COLUMN headers TEXT; -- JSON encoded {"Header":"Value"}
   ```

2. **Rust models**  
   * `ee/.../models.rs -> SourceData`
   * `WebDocumentDAO`, DTOs, GraphQL types

   Add field:

   ```rust
   /// Optional extra HTTP headers (JSON object)
   pub headers: Option<HashMap<String, String>>,
   ```

3. **Source REST / GraphQL**  
   Extend create / update endpoints to accept `headers`.

### 4.2  Background Job API

```rust
pub struct WebCrawlerJob {
    source_id: String,
    url: String,
    url_prefix: Option<String>,
    headers: Option<HashMap<String, String>>, // NEW
}
```

▶ Propagate from `WebDocumentService` when triggering the job.

### 4.3  Crawler Layer

1. **`tabby-crawler::crawl_url()`**  
   Accept `headers: &HashMap<…>` and append:

   ```rust
   for (k, v) in headers {
       command.arg("-H").arg(format!("{}: {}", k, v));
   }
   ```

2. **`reqwest` fetch in `crawler_llms()`**  
   Replace `reqwest::get()` with a `Client::builder().default_headers(headers.clone()).build()?`.

### 4.4  UI Updates (Optional MVP)

* Because the Admin UI currently shows only *Name* & *URL*, add an **Advanced** accordion with key-value header inputs.
* Until UI is ready, power-users can use the REST API directly.

### 4.5  Migration & Backwards Compatibility

* `headers` defaults to `NULL` → no behaviour change for existing sources.
* Feature flag env var `FILIN_ENABLE_CRAWLER_HEADERS` can guard the new path.

---

## 5. Impacted Tables & Code Locations

| Component | File / Table | Action |
|-----------|--------------|--------|
| DB | `web_documents` | add `headers` TEXT |
| Rust model | `SourceData`, `WebDocumentDAO` | new field |
| Service | `web_documents.rs` | persist field; pass to job |
| BG Job | `web_crawler.rs` | struct & serialization |
| Crawler | `tabby-crawler/lib.rs` | CLI args; reqwest headers |
| REST API | `external_groups/handlers.rs` | validate & expose |
| UI | `web/src/pages/DeveloperDocs*` | optional enhancement |

---

## 6. Estimated Effort

| Task | Complexity |
|------|------------|
| DB migration + model plumbing | **M** (≈1-2 h) |
| Job / crawler refactor | **M** (≈2-3 h) |
| REST + validation updates | **M** (≈1-2 h) |
| UI changes | **S** (optional) |
| Tests (unit + e2e crawler) | **M** |

Overall **1-2 days** of engineering work.

---

## 7. Quick Work-Around (No Code Change)

If Confluence allows **Basic-Auth in URL**, you could create the source with an *access-token-embedded URL*:

```
https://<user>:<PAT>@confluence.example.com/rest/api/content/1234
```

…but many servers disable this pattern, and credentials would be stored in plain-text, so the **proper header solution above is preferred**.

---

## 8. Next Steps

1. Approve the schema change & header field API.
2. Implement the backend changes (sections 4.1–4.3) behind a feature flag.
3. Add integration test with a mock HTTP server requiring an `Authorization` header.
4. Optional: extend Admin UI.

---

*End of document*

# Внедрение пользовательских HTTP-заголовков в веб-краулер Filin

*Автор: Cascade AI – 01 авг 2025 г.*

---

## 1. Что требуется

Нужно индексировать приватные страницы Confluence. Confluence принимает Personal Access Token (PAT) в HTTP-заголовке:

```
Authorization: Bearer <PAT>
```

Сейчас веб-краулер Filin **не** отправляет кастомные заголовки, поэтому не может получить защищённый контент. Требуется оценить, как добавить заголовки, и описать необходимые изменения.

---

## 2. Текущая архитектура краулинга (кратко)

| Слой | Файл/крейт | Назначение |
|------|-----------|------------|
| **CRUD источников** | `ee/tabby-webserver/src/routes/external_groups` | REST/GraphQL-эндпоинты создания «источников» (git, doc и пр.) |
| **Сервис веб-документов** | `ee/tabby-webserver/src/service/web_documents.rs` | Хранит web-documents в SQLite (`web_documents`) и запускает фоновые джобы краулера |
| **Фоновая джоба** | `ee/tabby-webserver/src/service/background_job/web_crawler.rs` (`WebCrawlerJob`) | Преобразует Source → краулинг, вызывает библиотеку краулера |
| **Библиотека краулера** | `crates/tabby-crawler` | Обёртка над [`katana`](https://github.com/projectdiscovery/katana); также скачивает `llms-full.txt` через `reqwest` |

> **Важно:** ни один слой не принимает и не передаёт заголовки. При этом `katana` умеет `-H "Header: value"`, но Filin не использует это.

### 2.1 Источник типа “doc” (Developer Docs)

* UI: **Settings → Context Sources → Developer Docs**  
* Таблица БД: `web_documents` (`id, name, url, preset, created_at, updated_at`)  
* REST: `POST /v1/external-groups/sources` с `source_type="doc"`  
* Фоновый запуск: `WebCrawlerJob::new(source_id, url, None)`

---

## 3. Пробелы — почему заголовки невозможны сейчас

1. **Схема** — в `SourceData` и таблице `web_documents` есть только `name`, `url`, без поля заголовков.  
2. **Интерфейс джобы** — `WebCrawlerJob` знает лишь `(source_id, url, url_prefix)`.  
3. **Вызов краулера** — `crawl_url()` формирует команду `katana` без `-H`.  
4. **Скачивание `llms-full.txt`** — отдельный `reqwest::get()` тоже без заголовков.

---

## 4. Предложение по реализации поддержки заголовков

### 4.1 Изменения модели данных

1. **База данных**  
   Добавить колонку `headers` (TEXT, JSON):

   ```sql
   ALTER TABLE web_documents ADD COLUMN headers TEXT; -- {"Header":"Value"}
   ```

2. **Rust-модели** (`SourceData`, `WebDocumentDAO` и др.):

   ```rust
   /// Дополнительные HTTP-заголовки
   pub headers: Option<HashMap<String, String>>,
   ```

3. **REST / GraphQL** — расширить create/update, чтобы принимать `headers`.

### 4.2 API фоновой джобы

```rust
pub struct WebCrawlerJob {
    source_id: String,
    url: String,
    url_prefix: Option<String>,
    headers: Option<HashMap<String, String>>, // НОВОЕ
}
```

Передавать из `WebDocumentService`.

### 4.3 Слой краулера

1. **`tabby-crawler::crawl_url()`** — принять `headers` и добавить к `katana`:

   ```rust
   for (k, v) in headers {
       command.arg("-H").arg(format!("{k}: {v}"));
   }
   ```

2. **`crawler_llms()`** — использовать `reqwest::Client` с `default_headers`.

### 4.4 UI (опционально)

* В форме источника добавить секцию **Advanced** с полями «Header / Value».
* Пока UI нет, можно работать через REST API.

### 4.5 Миграция и обратная совместимость

* `headers` по умолчанию `NULL`, поведение старых источников не меняется.
* Можно защитить фичу флагом `FILIN_ENABLE_CRAWLER_HEADERS`.

---

## 5. Затрагиваемые компоненты

| Компонент | Файл/таблица | Действие |
|-----------|--------------|----------|
| DB | `web_documents` | колонка `headers` |
| Модели | `SourceData`, `WebDocumentDAO` | новое поле |
| Сервис | `web_documents.rs` | сохранение и передача |
| Джоба | `web_crawler.rs` | поле + сериализация |
| Краулер | `tabby-crawler/lib.rs` | CLI `-H`, заголовки в `reqwest` |
| REST | `external_groups/handlers.rs` | валидация/экспонирование |
| UI | страницы Developer Docs | при необходимости |

---

## 6. Оценка трудозатрат

| Задача | Сложность |
|--------|-----------|
| Миграция + модели | **M** (≈1–2 ч) |
| Рефактор джобы/краулера | **M** (≈2–3 ч) |
| REST + валидация | **M** (≈1–2 ч) |
| UI | **S** (опц.) |
| Тесты (unit + e2e) | **M** |

Всего **1–2 дня** работы.

---

## 7. Быстрый обходной путь (без кода)

Если Confluence допускает **Basic-Auth в URL**, можно указать токен в ссылке:

```
https://<user>:<PAT>@confluence.example.com/rest/api/content/1234
```

Но это небезопасно, так что лучше реализовать заголовки.

---

## 8. Следующие шаги

1. Утвердить схему и API поля `headers`.  
2. Реализовать backend (п. 4.1–4.3) под фиче-флагом.  
3. Добавить интеграционный тест с мок-сервером, требующим `Authorization`.  
4. (Опц.) Расширить UI.

---

---

## Правила миграций в Filin

Filin использует **sqlx**-migrate. Каждая миграция состоит из двух файлов в каталоге `ee/tabby-db/migrations`:

* `<NNNN>_<slug>.up.sql` — изменения схемы **вперёд**.
* `<NNNN>_<slug>.down.sql` — откат.

Файлы нумеруются строго по возрастанию, четыре цифры. Названия должны быть короткими и описательными, например `0048_web_document_headers`.

При добавлении новой миграции нужно:

1. Создать оба файла в том же каталоге.
2. Обновить CI, если требуется (pipeline автоматически применяет все `.up.sql`).
3. Убедиться, что `down.sql` корректно откатывает изменения.

---

## Изменения в базе данных

| Таблица | Поле | Тип | Описание |
|---------|------|-----|----------|
| `web_documents` | `headers` | `TEXT` (nullable, JSON) | Сериализованная карта заголовков. Пример: `{ "Authorization": "Bearer …" }` |

> Для обратной совместимости колонка допускает `NULL`.

---

## Изменения внешнего API (`external_groups`)

1. **Расширить существующий `PUT /v1/external-groups/sources/{id}`**  
   • добавить опциональное поле `headers` в тело запроса.  
   • если поле отсутствует — поведение прежнее.  
   • валидировать, что JSON-объект строк-строк.

2. **Новый эндпоинт** `PATCH /v1/external-groups/sources/{id}/headers`  
   • принимает JSON-объект заголовков.  
   • перезаписывает колонку `headers`.  
   • возвращает обновлённый объект Source.  
   • удобно для UI, не затрагивает другие поля.

> Реализация находится в `ee/tabby-webserver/src/routes/external_groups/handlers.rs`. Нужно дополнить модели запросов/ответов в `models.rs` и обновить OpenAPI-аннотации.

---

## Задачи для Jira (порядок выполнения)

1. **Миграция `0048_web_document_headers`**  
   • `up.sql`: `ALTER TABLE web_documents ADD COLUMN headers TEXT;`  
   • `down.sql`: `ALTER TABLE web_documents DROP COLUMN headers;`
2. **Модели / DAO / GraphQL** — добавить поле `headers` (nullable `HashMap<String,String>`).
3. **Расширить API**  
   • обновить `UpdateSourceRequest`  
   • реализовать `PATCH /sources/{id}/headers`.
4. **Обновить `WebDocumentService`** — сохранять `headers` и передавать их в `WebCrawlerJob`.
5. **Расширить `WebCrawlerJob` + сериализацию** — новое поле `headers`.
6. **tabby-crawler**  
   • добавить поддержку CLI `-H`  
   • использовать `reqwest::Client::builder().default_headers()`.
7. **Тесты**  
   • unit: сохранение/загрузка заголовков из БД  
   • e2e: мок-сервер, требующий `Authorization`.
8. **(Опц.) UI** — форма ввода заголовков.
9. **Документация** — обновить README/Swagger.

---

*Конец документа (RU)*

'''

HTTP_HEADERS_RESEARCH.md
'''
# Исследование: Добавление HTTP заголовков в краулер Filin

## Обзор задачи

Требуется добавить поддержку HTTP заголовков в краулер Filin для индексации закрытых ресурсов, таких как Confluence с использованием PAT (Personal Access Token) токенов.

## 1. Текущая архитектура краулера

### Краткий обзор
Filin (форк Tabby) использует внешний инструмент **Katana** для веб-краулинга. Архитектура состоит из нескольких слоев:

1. **Web UI (React)** - пользовательский интерфейс для управления источниками документов
2. **GraphQL API** - API для создания и управления веб-документами  
3. **Database Layer** - SQLite база данных для хранения конфигурации документов
4. **Background Job System** - система фоновых заданий для запуска краулинга
5. **Crawler Service** - сервис, запускающий Katana как подпроцесс

### Детальная схема потока данных

```
Web UI → GraphQL Mutation → Database → Background Job → WebCrawlerJob → tabby-crawler → Katana
```

## 2. Анализ ключевых компонентов

### 2.1 Основной краулер (`/crates/tabby-crawler/src/lib.rs`)

**Текущая реализация:**
```rust
pub async fn crawl_pipeline(start_url: &str, prefix: &str) -> Result<impl Stream<Item = CrawledDocument> + Send + Unpin> {
    // Запуск Katana без HTTP заголовков
    let mut command = tokio::process::Command::new("katana")
        .args([
            "-jc",
            "-u", start_url,
            "-fs", prefix,
            "-fx",
            "-d", "10",
            "-c", "20",
            "-p", "20",
            "-rate-limit", "5",
            "-silent",
        ])
        .stdout(Stdio::piped())
        .stderr(Stdio::piped())
        .spawn()?;
}
```

**Проблемы:**
- ❌ Нет поддержки HTTP заголовков
- ❌ Katana запускается с фиксированными аргументами
- ❌ Невозможно передать токены авторизации

### 2.2 GraphQL схема (`/ee/tabby-schema/src/schema/web_documents.rs`)

**Текущая структура:**
```rust
#[derive(InputObject)]
pub struct CreateCustomDocumentInput {
    pub name: String,
    pub url: String,
}
```

**Проблемы:**
- ❌ Отсутствует поле для HTTP заголовков
- ❌ Валидация не учитывает заголовки

### 2.3 База данных (`/ee/tabby-db/src/web_documents.rs`)

**Текущая таблица `web_documents`:**
```sql
CREATE TABLE web_documents (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    url TEXT NOT NULL,
    is_preset BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Проблемы:**
- ❌ Нет поля для хранения HTTP заголовков
- ❌ Схема БД требует миграции

### 2.4 Фоновые задания (`/ee/tabby-webserver/src/service/background_job/web_crawler.rs`)

**Текущая структура WebCrawlerJob:**
```rust
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WebCrawlerJob {
    source_id: String,
    pub url: String,
    url_prefix: Option<String>,
}
```

**Проблемы:**
- ❌ Нет поля для HTTP заголовков
- ❌ Заголовки не передаются в краулер

## 3. Возможности Katana для HTTP заголовков

Katana поддерживает HTTP заголовки через флаг `-H`:

```bash
katana -u "https://example.com" -H "Authorization: Bearer token123" -H "X-Custom-Header: value"
```

**Возможности:**
- ✅ Поддержка множественных заголовков
- ✅ Поддержка всех стандартных HTTP заголовков
- ✅ Поддержка кастомных заголовков

## 4. Confluence как целевой источник

### Аутентификация в Confluence
Confluence поддерживает несколько методов аутентификации:

1. **Basic Auth** (username:password)
2. **Bearer Token** (PAT токены)
3. **API Key** (в заголовке)

**Пример для Confluence Cloud:**
```bash
# Personal Access Token
Authorization: Bearer <PAT_TOKEN>

# Basic Auth
Authorization: Basic <base64(username:password)>
```

## 5. Предлагаемое решение

### 5.1 Изменения в базе данных

**Миграция БД:**
```sql
-- Добавить поле для хранения HTTP заголовков
ALTER TABLE web_documents ADD COLUMN http_headers TEXT;
```

**Формат хранения (JSON):**
```json
{
  "Authorization": "Bearer your-pat-token",
  "X-Custom-Header": "custom-value"
}
```

### 5.2 Изменения в GraphQL схеме

**Новый input type:**
```rust
#[derive(InputObject)]
pub struct HttpHeader {
    pub name: String,
    pub value: String,
}

#[derive(InputObject)]
pub struct CreateCustomDocumentInput {
    pub name: String,
    pub url: String,
    pub headers: Option<Vec<HttpHeader>>,
}
```

### 5.3 Изменения в WebCrawlerJob

**Обновленная структура:**
```rust
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WebCrawlerJob {
    source_id: String,
    pub url: String,
    url_prefix: Option<String>,
    pub headers: Option<HashMap<String, String>>,
}
```

### 5.4 Изменения в краулере

**Обновленная функция crawl_pipeline:**
```rust
pub async fn crawl_pipeline(
    start_url: &str, 
    prefix: &str, 
    headers: Option<&HashMap<String, String>>
) -> Result<impl Stream<Item = CrawledDocument> + Send + Unpin> {
    let mut args = vec![
        "-jc", "-u", start_url, "-fs", prefix, "-fx",
        "-d", "10", "-c", "20", "-p", "20", 
        "-rate-limit", "5", "-silent"
    ];
    
    // Добавляем HTTP заголовки
    if let Some(headers) = headers {
        for (name, value) in headers {
            args.push("-H");
            args.push(&format!("{}:{}", name, value));
        }
    }
    
    let mut command = tokio::process::Command::new("katana")
        .args(args)
        .stdout(Stdio::piped())
        .stderr(Stdio::piped())
        .spawn()?;
    // ...
}
```

## 6. Список необходимых изменений

### 6.1 База данных
- [ ] **Миграция**: Добавить поле `http_headers TEXT` в таблицу `web_documents`
- [ ] **DAO**: Обновить методы создания/обновления для работы с заголовками
- [ ] **Валидация**: Добавить валидацию JSON формата заголовков

### 6.2 GraphQL API
- [ ] **Schema**: Добавить тип `HttpHeader` и поле `headers` в `CreateCustomDocumentInput`
- [ ] **Resolver**: Обновить резолверы для обработки заголовков
- [ ] **Validation**: Добавить валидацию заголовков (имена, значения)

### 6.3 Background Jobs
- [ ] **WebCrawlerJob**: Добавить поле `headers` в структуру
- [ ] **Serialization**: Обеспечить корректную сериализацию/десериализацию
- [ ] **Job Creation**: Передавать заголовки при создании заданий

### 6.4 Crawler Service
- [ ] **API**: Обновить `crawl_pipeline` для приема заголовков
- [ ] **Katana Integration**: Добавить формирование аргументов `-H` для Katana
- [ ] **Error Handling**: Обработка ошибок авторизации

### 6.5 Web UI
- [ ] **Components**: Создать компонент для ввода HTTP заголовков
- [ ] **Forms**: Интегрировать в форму создания веб-документов
- [ ] **Validation**: Клиентская валидация заголовков

## 7. Этапы реализации

### Этап 1: База данных и модели
1. Создать миграцию для добавления поля `http_headers`
2. Обновить Rust модели для работы с заголовками
3. Добавить методы сериализации/десериализации

### Этап 2: GraphQL API
1. Обновить GraphQL схему
2. Добавить валидацию заголовков
3. Обновить резолверы

### Этап 3: Краулер
1. Обновить `crawl_pipeline` для поддержки заголовков
2. Добавить интеграцию с Katana
3. Тестирование с различными заголовками

### Этап 4: Background Jobs
1. Обновить `WebCrawlerJob`
2. Передача заголовков в краулер
3. Тестирование job системы

### Этап 5: Web UI
1. Создать компоненты для ввода заголовков
2. Интегрировать в формы
3. UX тестирование

## 8. Примеры использования

### 8.1 Confluence с PAT токеном
```json
{
  "name": "Company Confluence",
  "url": "https://company.atlassian.net/wiki/",
  "headers": [
    {
      "name": "Authorization",
      "value": "Bearer your-confluence-pat-token"
    }
  ]
}
```

### 8.2 Защищенная документация с API ключом
```json
{
  "name": "Internal API Docs",
  "url": "https://internal-docs.company.com",
  "headers": [
    {
      "name": "X-API-Key",
      "value": "your-api-key"
    },
    {
      "name": "User-Agent",
      "value": "Filin-Crawler/1.0"
    }
  ]
}
```

## 9. Безопасность и рекомендации

### 9.1 Хранение секретов
- ⚠️ **Важно**: HTTP заголовки с токенами хранятся в открытом виде в БД
- 🔐 **Рекомендация**: Рассмотреть шифрование чувствительных заголовков
- 🔒 **Альтернатива**: Интеграция с системой управления секретами

### 9.2 Валидация
- ✅ Валидировать имена заголовков (RFC 7230)
- ✅ Ограничить размер значений заголовков
- ✅ Предотвратить инъекции в командную строку

### 9.3 Логирование
- ⚠️ Не логировать значения заголовков с токенами
- ✅ Логировать только имена заголовков для отладки

## 10. Оценка сложности

### Низкая сложность ✅
- Изменения в GraphQL схеме
- Добавление полей в структуры
- Базовая интеграция с Katana

### Средняя сложность ⚠️
- Миграция базы данных
- Обновление всей цепочки передачи данных
- Web UI компоненты

### Высокая сложность ❌
- Шифрование секретов (если требуется)
- Сложная валидация заголовков
- Backward compatibility

## 11. Заключение

Добавление поддержки HTTP заголовков в краулер Filin **возможно и реализуемо**. Основные изменения требуются в 5 слоях архитектуры, но каждое изменение относительно простое.

**Время реализации**: ~1-2 недели для полной реализации
**Сложность**: Средняя (требует изменений во всей цепочке)
**Риски**: Минимальные (Katana уже поддерживает заголовки)

Это решение откроет возможность индексации защищенных ресурсов, включая Confluence, внутренние документации и API, значительно расширив возможности системы.

'''

