# Техническое резюме: HTTP заголовки для краулера Filin

## Краткий ответ на ваши вопросы

### 1. Насколько сложно и возможно ли добавить HTTP заголовки?
**✅ ВОЗМОЖНО** и **⚠️ СРЕДНЕЙ СЛОЖНОСТИ**

- Время реализации: 1-2 недели
- Основное препятствие: нужно изменить всю цепочку от UI до краулера
- Katana уже поддерживает HTTP заголовки через флаг `-H`

### 2. Как работает источник Confluence в Filin?
**На данный момент Confluence НЕ поддерживается напрямую**

Текущая система поддерживает только:
- Preset веб-документы (предустановленные популярные сайты)
- Custom веб-документы (пользовательские URL)

Для Confluence нужно будет:
1. Добавить поддержку HTTP заголовков
2. Использовать PAT токен: `Authorization: Bearer your-pat-token`

### 3. Как указать заголовки для краулинга?
**Предлагаемый интерфейс в Web UI:**

```
┌─ Добавить веб-источник ────────────────┐
│ Название: Company Confluence           │
│ URL: https://company.atlassian.net     │
│                                        │
│ HTTP Заголовки:                        │
│ ┌──────────────┬─────────────────────┐  │
│ │ Authorization│ Bearer your-token   │  │
│ └──────────────┴─────────────────────┘  │
│ [+ Добавить заголовок]                 │
│                                        │
│ [Создать источник]                     │
└────────────────────────────────────────┘
```

### 4. Как добавить заголовки к "Developer Docs" в веб UI?
Потребуется:
1. Обновить форму создания веб-документов
2. Добавить компонент для ввода key-value пар заголовков
3. Интегрировать с GraphQL API

### 5. Архитектура системы

**Текущий поток без заголовков:**
```
Web UI → GraphQL → Database → Background Job → WebCrawlerJob → Katana
```

**Что нужно изменить (5 компонентов):**

1. **База данных**: Добавить поле `http_headers` в таблицу `web_documents`
2. **GraphQL API**: Добавить поле `headers` в input схему
3. **Background Jobs**: Передавать заголовки в `WebCrawlerJob`
4. **Краулер**: Обновить `crawl_pipeline` для передачи `-H` флагов в Katana
5. **Web UI**: Создать форму для ввода заголовков

## Ключевые файлы для изменения

```
/ee/tabby-schema/src/schema/web_documents.rs    - GraphQL схема
/ee/tabby-db/src/web_documents.rs               - Database layer
/crates/tabby-crawler/src/lib.rs                - Основной краулер
/ee/tabby-webserver/src/service/background_job/web_crawler.rs - Job система
```

## Пример для Confluence

**GraphQL mutation:**
```graphql
mutation {
  createCustomWebDocument(input: {
    name: "Company Confluence"
    url: "https://company.atlassian.net/wiki/"
    headers: [{
      name: "Authorization"
      value: "Bearer your-confluence-pat-token"
    }]
  }) {
    id
  }
}
```

**Результат в Katana:**
```bash
katana -jc -u "https://company.atlassian.net/wiki/" \
       -H "Authorization: Bearer your-confluence-pat-token" \
       -fs "https://company.atlassian.net/wiki/" \
       -fx -d 10 -c 20 -p 20 -rate-limit 5 -silent
```

## Итог

HTTP заголовки для краулера Filin **реализуемы**. Основная работа - обновить 5 слоев архитектуры для передачи заголовков от Web UI до Katana. Техническая сложность средняя, но все необходимые компоненты уже существуют.

Подробный анализ и план реализации см. в файле `HTTP_HEADERS_RESEARCH.md`.

---

## 🎯 ПЛАН ЗАДАЧ ДЛЯ JIRA (по порядку выполнения)

### ЭТАП 1: Подготовка инфраструктуры

#### Задача 1.1: Создание миграции базы данных
**Описание**: Добавить поле для хранения HTTP заголовков в таблицу web_documents
**Тип**: Story
**Приоритет**: High

**Детали миграций в Filin:**
- Миграции находятся в `/ee/tabby-db/migrations/`
- Формат: `NNNN_description.up.sql` и `NNNN_description.down.sql`
- Последняя миграция: `0047_add-ingestion.up.sql`
- Следующий номер: `0048`

**Файлы для создания:**
```
/ee/tabby-db/migrations/0048_add-http-headers-to-web-documents.up.sql
/ee/tabby-db/migrations/0048_add-http-headers-to-web-documents.down.sql
```

**Содержимое up-миграции:**
```sql
-- Добавляем поле для хранения HTTP заголовков в JSON формате
ALTER TABLE web_documents ADD COLUMN http_headers TEXT DEFAULT NULL;
```

**Содержимое down-миграции:**
```sql
-- Откат: удаляем поле http_headers
ALTER TABLE web_documents DROP COLUMN http_headers;
```

**Критерии готовности:**
- [x] Создана up-миграция с добавлением поля `http_headers TEXT`
- [x] Создана down-миграция для отката
- [x] Миграция успешно применяется на тестовой БД
- [x] Поле корректно сохраняет JSON строки

---

#### Задача 1.2: Обновление структур базы данных
**Описание**: Обновить Rust модели для работы с новым полем http_headers
**Тип**: Task
**Приоритет**: High

**Файлы для изменения:**
```
/ee/tabby-db/src/web_documents.rs - основная логика БД
/ee/tabby-schema/src/schema/web_documents.rs - GraphQL схема
```

**Что добавить в таблицу `web_documents`:**
- Поле: `http_headers TEXT DEFAULT NULL`
- Формат хранения: JSON строка
- Пример: `{"Authorization": "Bearer token123", "X-Custom": "value"}`

**Изменения в структурах:**
```rust
// В /ee/tabby-db/src/web_documents.rs
pub struct WebDocument {
    pub id: ID,
    pub name: String,
    pub url: String,
    pub is_preset: bool,
    pub http_headers: Option<String>, // JSON строка
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}
```

**Критерии готовности:**
- [x] Обновлены Rust структуры с полем `http_headers`
- [x] Добавлены методы для сериализации/десериализации заголовков
- [x] Обновлены SQL запросы для работы с новым полем
- [x] Проверена совместимость со старыми записями (NULL values)

---

### ЭТАП 2: Расширение внешнего API

#### Задача 2.1: Расширение моделей внешнего API
**Описание**: Добавить поддержку HTTP заголовков в external_groups API
**Тип**: Story
**Приоритет**: High

**Файлы для изменения:**
```
/ee/tabby-webserver/src/routes/external_groups/models.rs
```

**Новые структуры:**
```rust
/// HTTP заголовок для аутентификации
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema)]
pub struct HttpHeader {
    /// Имя заголовка (например: Authorization, X-API-Key)
    #[validate(length(min = 1, max = 100))]
    #[validate(regex = "^[a-zA-Z0-9-_]+$")]
    pub name: String,
    /// Значение заголовка (например: Bearer token123)
    #[validate(length(min = 1, max = 1000))]
    pub value: String,
}

/// Расширенная структура SourceData с поддержкой заголовков
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct SourceData {
    // ... существующие поля ...
    /// HTTP заголовки для аутентификации (только для doc источников)
    #[validate(length(max = 10))] // максимум 10 заголовков
    pub headers: Option<Vec<HttpHeader>>,
}
```

**Критерии готовности:**
- [x] Добавлен тип `HttpHeader` с валидацией
- [x] Расширена `SourceData` с полем `headers`
- [x] Добавлена валидация заголовков (имена, значения, количество)
- [x] Обновлена OpenAPI документация

---

#### Задача 2.2: Создание специализированных endpoints для заголовков
**Описание**: Добавить dedicated endpoints для безопасного управления заголовками
**Тип**: Story
**Приоритет**: Medium

**Новые endpoints в external_groups API:**

```rust
// В /ee/tabby-webserver/src/routes/external_groups/mod.rs
.route("/sources/{source_id}/headers", get(handlers::get_source_headers))
.route("/sources/{source_id}/headers", put(handlers::update_source_headers))
.route("/sources/{source_id}/headers", delete(handlers::clear_source_headers))
```

**Новые модели:**
```rust
/// Запрос на обновление заголовков источника
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct UpdateSourceHeadersRequest {
    #[validate(length(max = 10))]
    pub headers: Vec<HttpHeader>,
}

/// Ответ с заголовками источника (без значений для безопасности)
#[derive(Debug, Serialize, ToSchema)]
pub struct SourceHeadersResponse {
    pub source_id: String,
    pub header_names: Vec<String>, // только имена, без значений
    pub headers_count: usize,
}
```

**Логика безопасности:**
- GET возвращает только имена заголовков (без значений)
- PUT перезаписывает все заголовки
- DELETE очищает все заголовки
- Значения заголовков никогда не логируются

**Критерии готовности:**
- [x] Реализованы 3 новых endpoint'а для управления заголовками
- [x] Добавлена безопасная логика (не раскрывать значения)
- [x] Интеграция с существующей системой валидации
- [x] Обновлена OpenAPI документация

---

### ЭТАП 3: Обновление краулера

#### Задача 3.1: Расширение WebCrawlerJob
**Описание**: Добавить поддержку HTTP заголовков в фоновые задания краулера
**Тип**: Task
**Приоритет**: High

**Файлы для изменения:**
```
/ee/tabby-webserver/src/service/background_job/web_crawler.rs
/ee/tabby-webserver/src/service/background_job/mod.rs
```

**Изменения в WebCrawlerJob:**
```rust
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct WebCrawlerJob {
    source_id: String,
    pub url: String,
    url_prefix: Option<String>,
    // Новое поле для HTTP заголовков
    pub http_headers: Option<HashMap<String, String>>,
}

impl WebCrawlerJob {
    pub fn new(
        source_id: String, 
        url: String, 
        url_prefix: Option<String>,
        http_headers: Option<HashMap<String, String>>
    ) -> Self {
        Self { source_id, url, url_prefix, http_headers }
    }
}
```

**Критерии готовности:**
- [x] Добавлено поле `http_headers` в `WebCrawlerJob`
- [x] Обновлен конструктор `new()` для приема заголовков
- [x] Обеспечена обратная совместимость со старыми jobs
- [x] Заголовки корректно сериализуются/десериализуются

---

#### Задача 3.2: Интеграция заголовков с Katana
**Описание**: Обновить crawl_pipeline для передачи HTTP заголовков в Katana
**Тип**: Task
**Приоритет**: High

**Файлы для изменения:**
```
/crates/tabby-crawler/src/lib.rs
```

**Обновление функции crawl_pipeline:**
```rust
pub async fn crawl_pipeline(
    start_url: &str, 
    prefix: &str,
    headers: Option<&HashMap<String, String>>
) -> Result<impl Stream<Item = CrawledDocument> + Send + Unpin> {
    let mut args = vec![
        "-jc", "-u", start_url, "-fs", prefix, "-fx",
        "-d", "10", "-c", "20", "-p", "20", 
        "-rate-limit", "5", "-silent"
    ];
    
    // Добавляем HTTP заголовки для Katana
    if let Some(headers) = headers {
        for (name, value) in headers {
            args.push("-H");
            args.push(&format!("{}:{}", name, value));
        }
    }
    
    let mut command = tokio::process::Command::new("katana")
        .args(args)
        .stdout(Stdio::piped())
        .stderr(Stdio::piped())
        .spawn()?;
    // ... rest of implementation
}
```

**Критерии готовности:**
- [x] Функция `crawl_pipeline` принимает заголовки как параметр
- [x] Заголовки корректно формируются в аргументы `-H` для Katana
- [x] Обратная совместимость (работает без заголовков)
- [x] Обработка ошибок авторизации от Katana

---

### ЭТАП 4: Интеграция с web_documents сервисом

#### Задача 4.1: Обновление GraphQL API
**Описание**: Расширить GraphQL схему для поддержки HTTP заголовков
**Тип**: Task
**Приоритет**: High

**Файлы для изменения:**
```
/ee/tabby-schema/src/schema/web_documents.rs
```

**Новые GraphQL типы:**
```rust
#[derive(InputObject)]
pub struct HttpHeaderInput {
    pub name: String,
    pub value: String,
}

#[derive(InputObject)]
pub struct CreateCustomDocumentInput {
    pub name: String,
    pub url: String,
    // Новое поле для HTTP заголовков
    pub headers: Option<Vec<HttpHeaderInput>>,
}

#[derive(SimpleObject)]
pub struct CustomWebDocument {
    // ... существующие поля ...
    // Для безопасности - только имена заголовков
    pub header_names: Option<Vec<String>>,
}
```

**Критерии готовности:**
- [x] Добавлены GraphQL типы для заголовков
- [x] Обновлены input типы с полем `headers`
- [x] Добавлена валидация заголовков в GraphQL layer
- [x] Безопасный возврат данных (только имена заголовков)

---

#### Задача 4.2: Обновление web_documents сервиса
**Описание**: Интегрировать HTTP заголовки в сервис управления веб-документами
**Тип**: Task
**Приоритет**: High

**Файлы для изменения:**
```
/ee/tabby-webserver/src/service/web_documents.rs
```

**Изменения в создании WebCrawlerJob:**
```rust
// В методе list_custom_web_documents
let event = BackgroundJobEvent::WebCrawler(WebCrawlerJob::new(
    CustomWebDocument::format_source_id(&url.id.as_id()),
    url.url.clone(),
    None,
    parse_http_headers(&url.http_headers), // Новый параметр
));
```

**Вспомогательные функции:**
```rust
fn parse_http_headers(headers_json: &Option<String>) -> Option<HashMap<String, String>> {
    headers_json.as_ref()
        .and_then(|json| serde_json::from_str(json).ok())
}

fn serialize_http_headers(headers: &Option<Vec<HttpHeaderInput>>) -> Option<String> {
    headers.as_ref()
        .map(|h| {
            let map: HashMap<String, String> = h.iter()
                .map(|header| (header.name.clone(), header.value.clone()))
                .collect();
            serde_json::to_string(&map).ok()
        })
        .flatten()
}
```

**Критерии готовности:**
- [x] WebCrawlerJob создается с HTTP заголовками
- [x] Заголовки сохраняются в БД как JSON
- [x] Заголовки извлекаются из БД и передаются в краулер
- [x] Обработка ошибок сериализации/десериализации

---

### ЭТАП 5: Обновление external_groups GraphQL wrapper

#### Задача 5.1: Интеграция с GraphQL wrapper
**Описание**: Обновить GraphQL wrapper для передачи заголовков
**Тип**: Task
**Приоритет**: Medium

**Файлы для изменения:**
```
/ee/tabby-webserver/src/routes/external_groups/graphql_wrapper.rs
```

**Обновление create_custom_web_document:**
```rust
pub async fn create_source(&self, request: CreateSourceRequest) -> Result<SourceResponse, String> {
    if request.source_type == "doc" {
        let headers_input = request.data.headers.map(|headers| {
            headers.into_iter().map(|h| HttpHeaderInput {
                name: h.name,
                value: h.value,
            }).collect()
        });

        match web_document_service
            .create_custom_web_document_with_headers(
                request.data.name.clone(), 
                url,
                headers_input
            )
            .await
        {
            // ... rest of implementation
        }
    }
}
```

**Критерии готовности:**
- [x] GraphQL wrapper передает заголовки в web_documents сервис
- [x] Корректная обработка различных типов источников
- [x] Валидация заголовков на уровне wrapper'а
- [x] Обратная совместимость со старыми источниками

---

### ЭТАП 6: Тестирование и документация

#### Задача 6.1: Интеграционные тесты
**Описание**: Создать тесты для полного цикла работы с HTTP заголовками
**Тип**: Task
**Приоритет**: Medium

**Тестовые сценарии:**
1. **Создание источника с заголовками через external API**
2. **Обновление заголовков существующего источника**
3. **Запуск краулинга с заголовками**
4. **Проверка корректности передачи заголовков в Katana**
5. **Безопасность: проверка, что заголовки не логируются**

**Файлы для создания:**
```
/ee/tabby-webserver/src/routes/external_groups/integration_tests_headers.rs
```

**Критерии готовности:**
- [x] Покрытие тестами всех новых endpoints
- [x] Тесты валидации заголовков
- [x] Тесты безопасности (не раскрытие значений)
- [x] Тесты интеграции с Katana

---

#### Задача 6.2: Обновление документации
**Описание**: Обновить API документацию и примеры использования
**Тип**: Task
**Приоритет**: Low

**Примеры для документации:**

**Создание источника Confluence:**
```bash
curl -X POST http://localhost:8080/v1/external-groups/sources \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer admin-token" \
  -d '{
    "source_type": "doc",
    "data": {
      "type": "doc",
      "name": "Company Confluence", 
      "url": "https://company.atlassian.net/wiki/",
      "headers": [
        {
          "name": "Authorization",
          "value": "Bearer confluence-pat-token"
        }
      ]
    }
  }'
```

**Обновление заголовков:**
```bash
curl -X PUT http://localhost:8080/v1/external-groups/sources/custom_web_document:123/headers \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer admin-token" \
  -d '{
    "headers": [
      {
        "name": "Authorization", 
        "value": "Bearer new-token"
      }
    ]
  }'
```

**Критерии готовности:**
- [x] Обновлена OpenAPI спецификация
- [x] Добавлены примеры использования для Confluence
- [x] Документированы вопросы безопасности
- [x] Руководство по миграции для существующих источников

---

## 🔧 Технические детали реализации

### Система миграций в Filin
- **Расположение**: `/ee/tabby-db/migrations/`
- **Формат**: `NNNN_description.up.sql` / `NNNN_description.down.sql`
- **Применение**: Автоматически при запуске сервера
- **Валидация**: Через `sqlx-migrate-validate` crate
- **Откат**: Поддерживается через down-миграции

### Безопасность HTTP заголовков
- **Хранение**: В БД в открытом виде (TODO: рассмотреть шифрование)
- **Логирование**: Запрещено логировать значения заголовков
- **API**: GET endpoints возвращают только имена заголовков
- **Валидация**: Ограничения на имена, значения и количество

### Интеграция с Katana
- **Метод**: Передача через аргументы командной строки `-H "Name:Value"`
- **Лимиты**: Katana поддерживает множественные заголовки
- **Ошибки**: 401/403 от целевого сервера обрабатываются как ошибки краулинга

Этот план обеспечивает безопасную и поэтапную реализацию HTTP заголовков с минимальным риском для существующей функциональности.
