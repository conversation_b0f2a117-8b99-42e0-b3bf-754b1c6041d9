# Исследование: Добавление HTTP заголовков в краулер Filin

## Постановка задачи

Необходимо добавить возможность передачи HTTP заголовков в краулер Filin для индексации закрытых ресурсов, таких как Confluence с Personal Access Token (PAT) через заголовок Authorization.

## Что от вас хотят

1. **Проанализировать архитектуру** краулинга в Filin
2. **Определить точки интеграции** для добавления HTTP заголовков
3. **Предложить решение** для передачи заголовков через веб-интерфейс
4. **Оценить сложность** реализации

## Текущая архитектура краулинга

### Компоненты системы

| Слой | Файл/Компонент | Назначение |
|------|----------------|------------|
| **Web UI** | `ee/tabby-ui/app/(dashboard)/settings/(integrations)/providers/doc/` | React компоненты для создания источников |
| **GraphQL API** | `ee/tabby-schema/src/schema/web_documents.rs` | Схема и типы данных |
| **Сервис** | `ee/tabby-webserver/src/service/web_documents.rs` | Бизнес-логика управления документами |
| **База данных** | `ee/tabby-db/src/web_documents.rs` | DAO для работы с БД |
| **Фоновые задачи** | `ee/tabby-webserver/src/service/background_job/web_crawler.rs` | Запуск краулинга |
| **Краулер** | `crates/tabby-crawler/src/lib.rs` | Обертка над Katana CLI |

### Текущая структура БД

```sql
CREATE TABLE web_documents(
    id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    url TEXT NOT NULL,
    is_preset BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT(DATETIME('now')),
    updated_at TIMESTAMP NOT NULL DEFAULT(DATETIME('now')),
    CONSTRAINT idx_name UNIQUE(name),
    CONSTRAINT idx_url UNIQUE(url)
);
```

### Текущий GraphQL API

```graphql
input CreateCustomDocumentInput {
    name: String!
    url: String!
}

type Mutation {
    createCustomDocument(input: CreateCustomDocumentInput!): ID!
}
```

## Источник "Developer Docs" и веб-интерфейс

В веб-интерфейсе пользователь видит форму с полями:
- **Name** - название источника
- **URL** - адрес для краулинга

Эта форма находится в `ee/tabby-ui/app/(dashboard)/settings/(integrations)/providers/doc/components/create-custom-doc.tsx`

## Как работает краулер

Filin использует внешний инструмент **Katana** (Project Discovery) для краулинга:

1. **Запуск Katana** как внешний процесс через `tokio::process::Command`
2. **Передача параметров** через аргументы командной строки
3. **Получение результатов** в формате JSONL

### Текущие параметры Katana

```rust
command
    .arg("-u").arg(start_url)
    .arg("-jsonl")
    .arg("-crawl-scope").arg(format!("{}.*", regex::escape(prefix_url)))
    .arg("-depth").arg("9999")
    .arg("-max-response-size").arg("10485760")
    .arg("-rate-limit-minute").arg("120")
    .arg("-strategy").arg("breadth-first")
```

### Поддержка HTTP заголовков в Katana

Katana поддерживает HTTP заголовки через флаг `-H`:

```bash
katana -u https://example.com -H 'Authorization: Bearer token123'
katana -u https://example.com -H 'Cookie: session=abc123'
```

Можно передавать несколько заголовков:
```bash
katana -u https://example.com -H 'Authorization: Bearer token' -H 'X-API-Key: key123'
```

## Предлагаемое решение

### 1. Расширение схемы базы данных

```sql
-- Новая миграция
ALTER TABLE web_documents ADD COLUMN http_headers TEXT;
```

Поле `http_headers` будет содержать JSON с заголовками:
```json
[
  {"name": "Authorization", "value": "Bearer token123"},
  {"name": "X-API-Key", "value": "api-key-value"}
]
```

### 2. Обновление GraphQL схемы

```rust
#[derive(GraphQLInputObject)]
pub struct HttpHeaderInput {
    pub name: String,
    pub value: String,
}

#[derive(Validate, GraphQLInputObject)]
pub struct CreateCustomDocumentInput {
    #[validate(regex(...))]
    pub name: String,
    #[validate(url(...))]
    pub url: String,
    pub http_headers: Option<Vec<HttpHeaderInput>>,
}
```

### 3. Обновление DAO

```rust
#[derive(FromRow)]
pub struct WebDocumentDAO {
    pub id: i64,
    pub name: String,
    pub url: String,
    pub is_preset: bool,
    pub http_headers: Option<String>, // JSON строка
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl DbConn {
    pub async fn create_web_document(
        &self,
        name: String,
        url: String,
        is_preset: bool,
        http_headers: Option<String>,
    ) -> Result<i64> {
        // Обновленная реализация
    }
}
```

### 4. Обновление сервиса

```rust
impl WebDocumentService for WebDocumentServiceImpl {
    async fn create_custom_web_document(
        &self, 
        name: String, 
        url: String,
        headers: Option<Vec<HttpHeader>>,
    ) -> Result<ID> {
        let headers_json = headers.map(|h| serde_json::to_string(&h).unwrap());
        let id = self.db.create_web_document(name, url, false, headers_json).await?;
        
        let job = WebCrawlerJob::new_with_headers(
            CustomWebDocument::format_source_id(&id.as_id()),
            url,
            None,
            headers,
        );
        
        self.job_service.trigger(job.to_command()).await;
        Ok(id.as_id())
    }
}
```

### 5. Обновление краулера

```rust
pub async fn crawl_pipeline_with_headers(
    start_url: &str,
    prefix_url: &str,
    headers: Option<&[HttpHeader]>,
) -> anyhow::Result<impl Stream<Item = CrawledDocument>> {
    let mut command = tokio::process::Command::new("katana");
    
    // Существующие аргументы...
    command.arg("-u").arg(start_url)
           .arg("-jsonl")
           .arg("-crawl-scope").arg(format!("{}.*", regex::escape(prefix_url)));
    
    // Добавление HTTP заголовков
    if let Some(headers) = headers {
        for header in headers {
            command.arg("-H").arg(format!("{}:{}", header.name, header.value));
        }
    }
    
    // Остальная логика...
}
```

### 6. Обновление веб-интерфейса

В `ee/tabby-ui/app/(dashboard)/settings/(integrations)/providers/doc/components/create-custom-doc.tsx`:

```typescript
const formSchema = z.object({
  name: z.string().trim(),
  url: z.string().url().trim(),
  httpHeaders: z.array(z.object({
    name: z.string().min(1),
    value: z.string().min(1)
  })).optional()
})

// Добавить компонент для управления заголовками
const HttpHeadersInput = () => {
  // Динамическое добавление/удаление заголовков
  // Поля: Header Name, Header Value
  // Кнопки: Add Header, Remove Header
}
```

## Затрагиваемые таблицы и файлы

### База данных
- **Таблица**: `web_documents` - добавление поля `http_headers`
- **Миграция**: новый файл в `ee/tabby-db/migrations/`

### Backend файлы
1. `ee/tabby-db/src/web_documents.rs` - обновление DAO
2. `ee/tabby-schema/src/schema/web_documents.rs` - GraphQL схема
3. `ee/tabby-webserver/src/service/web_documents.rs` - бизнес-логика
4. `ee/tabby-webserver/src/service/background_job/web_crawler.rs` - фоновые задачи
5. `crates/tabby-crawler/src/lib.rs` - краулер

### Frontend файлы
1. `ee/tabby-ui/app/(dashboard)/settings/(integrations)/providers/doc/components/create-custom-doc.tsx`
2. Возможно новые компоненты для управления заголовками

## Оценка сложности

### Низкая сложность ⭐⭐☆☆☆
- **Время реализации**: 1-2 дня
- **Риски**: Минимальные
- **Причины**:
  - Katana уже поддерживает HTTP заголовки
  - Архитектура позволяет легко добавить новые поля
  - Изменения локализованы и не затрагивают критическую логику

### Этапы реализации

1. **Миграция БД** (30 мин)
2. **Обновление DAO** (1 час)
3. **Обновление GraphQL схемы** (1 час)
4. **Обновление сервиса** (2 часа)
5. **Обновление краулера** (2 часа)
6. **Обновление веб-интерфейса** (4 часа)
7. **Тестирование** (2 часа)

## Пример использования

После реализации пользователь сможет:

1. Зайти в настройки источников данных
2. Создать новый источник "Confluence"
3. Указать URL: `https://oneproject.it-one.ru/confluence/`
4. Добавить заголовок:
   - **Name**: `Authorization`
   - **Value**: `Bearer <PAT_TOKEN>`
5. Сохранить и запустить индексацию

Краулер будет использовать указанные заголовки для доступа к закрытым страницам Confluence.

## Вопросы безопасности

### Хранение чувствительных данных
- **Проблема**: HTTP заголовки могут содержать токены и пароли
- **Решение**: Рассмотреть шифрование поля `http_headers` в БД
- **Альтернатива**: Использовать переменные окружения или внешние хранилища секретов

### Логирование
- **Риск**: Заголовки могут попасть в логи
- **Решение**: Маскировать чувствительные заголовки в логах
- **Реализация**: Добавить фильтрацию в `logkit::info!` вызовы

### Доступ к заголовкам
- **Ограничение**: Только администраторы должны видеть/редактировать заголовки
- **Реализация**: Проверка прав доступа в GraphQL резолверах

## Альтернативные подходы

### 1. Переменные окружения
```rust
// Вместо хранения в БД, использовать env переменные
let auth_header = std::env::var("CONFLUENCE_AUTH_TOKEN")?;
command.arg("-H").arg(format!("Authorization: Bearer {}", auth_header));
```

### 2. Внешний конфиг файл
```yaml
# config/crawler-headers.yaml
sources:
  confluence:
    headers:
      - name: Authorization
        value: "Bearer ${CONFLUENCE_TOKEN}"
```

### 3. Интеграция с системами управления секретами
- HashiCorp Vault
- AWS Secrets Manager
- Azure Key Vault

## Дополнительные возможности

### Поддержка шаблонов
```json
[
  {"name": "Authorization", "value": "Bearer ${CONFLUENCE_TOKEN}"},
  {"name": "X-User-ID", "value": "${USER_ID}"}
]
```

### Предустановленные шаблоны
- **Confluence PAT**: `Authorization: Bearer {token}`
- **Basic Auth**: `Authorization: Basic {base64(user:pass)}`
- **API Key**: `X-API-Key: {key}`

### Валидация заголовков
```rust
fn validate_headers(headers: &[HttpHeader]) -> Result<()> {
    for header in headers {
        // Проверка на безопасные имена заголовков
        if header.name.to_lowercase().contains("password") {
            return Err("Небезопасное имя заголовка");
        }
        // Проверка формата
        if !header.name.chars().all(|c| c.is_ascii_alphanumeric() || c == '-') {
            return Err("Неверный формат имени заголовка");
        }
    }
    Ok(())
}
```

## Тестирование

### Unit тесты
```rust
#[cfg(test)]
mod tests {
    #[tokio::test]
    async fn test_crawl_with_headers() {
        let headers = vec![
            HttpHeader { name: "Authorization".to_string(), value: "Bearer test".to_string() }
        ];
        let result = crawl_pipeline_with_headers("https://example.com", "https://example.com", Some(&headers)).await;
        assert!(result.is_ok());
    }
}
```

### Интеграционные тесты
1. Создание источника с заголовками через GraphQL
2. Проверка передачи заголовков в Katana
3. Тестирование с реальным Confluence

## Заключение

Добавление поддержки HTTP заголовков в краулер Filin является относительно простой задачей благодаря:
- Готовой поддержке в Katana
- Хорошо структурированной архитектуре
- Минимальным изменениям в существующем коде

**Рекомендации**:
1. Начать с базовой реализации (хранение в БД)
2. Добавить валидацию и маскировку в логах
3. Рассмотреть интеграцию с системами управления секретами для продакшена

Решение позволит индексировать закрытые ресурсы с различными типами аутентификации (Bearer tokens, API keys, cookies) и откроет возможности для интеграции с корпоративными системами.

## Система миграций в Filin

### Как устроены миграции

Filin использует **SQLX** для управления базой данных и миграциями:

- **Расположение**: `ee/tabby-db/migrations/`
- **Формат**: Парные файлы `.up.sql` и `.down.sql`
- **Нумерация**: Последовательная нумерация `0001_`, `0002_`, etc.
- **Схема**: Локальная копия схемы в `ee/tabby-db/schema.sqlite`

### Команды для работы с миграциями

```bash
# Установка SQLX CLI
cargo install sqlx-cli

# Создание новой миграции
cargo sqlx migrate add --source ee/tabby-db/migrations -r -s add_http_headers_to_web_documents

# Применение миграций
cargo sqlx migrate run --source ee/tabby-db/migrations

# Пересоздание схемы
rm ee/tabby-db/schema.sqlite
cargo sqlx db setup --source ee/tabby-db/migrations

# Обновление схемы после изменений
make update-db-schema
```

### Пример структуры миграции

Для HTTP заголовков потребуется создать миграцию `0048_add-http-headers-to-web-documents`:

**0048_add-http-headers-to-web-documents.up.sql:**
```sql
-- Добавление поля для HTTP заголовков в таблицу web_documents
ALTER TABLE web_documents ADD COLUMN http_headers TEXT;

-- Создание индекса для быстрого поиска документов с заголовками
CREATE INDEX idx_web_documents_http_headers ON web_documents(http_headers)
WHERE http_headers IS NOT NULL;
```

**0048_add-http-headers-to-web-documents.down.sql:**
```sql
-- Откат изменений
DROP INDEX IF EXISTS idx_web_documents_http_headers;
ALTER TABLE web_documents DROP COLUMN http_headers;
```

### Что необходимо учесть при миграциях

1. **Обратная совместимость**: Поле должно быть `NULL`-able для существующих записей
2. **Валидация JSON**: Добавить CHECK constraint для валидации JSON формата
3. **Индексы**: Создать индекс для оптимизации запросов
4. **Тестирование**: Обновить тесты миграций в `ee/tabby-db/src/migration_tests.rs`

## Поля базы данных

### Текущая структура таблицы web_documents

```sql
CREATE TABLE web_documents(
    id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    url TEXT NOT NULL,
    is_preset BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT(DATETIME('now')),
    updated_at TIMESTAMP NOT NULL DEFAULT(DATETIME('now')),
    CONSTRAINT idx_name UNIQUE(name),
    CONSTRAINT idx_url UNIQUE(url)
);
```

### Новое поле для HTTP заголовков

```sql
-- Добавляемое поле
http_headers TEXT, -- JSON строка с массивом заголовков

-- Пример содержимого:
-- [{"name": "Authorization", "value": "Bearer token123"}, {"name": "X-API-Key", "value": "key456"}]
```

### Полная структура после миграции

```sql
CREATE TABLE web_documents(
    id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    url TEXT NOT NULL,
    is_preset BOOLEAN NOT NULL DEFAULT FALSE,
    http_headers TEXT, -- НОВОЕ ПОЛЕ
    created_at TIMESTAMP NOT NULL DEFAULT(DATETIME('now')),
    updated_at TIMESTAMP NOT NULL DEFAULT(DATETIME('now')),
    CONSTRAINT idx_name UNIQUE(name),
    CONSTRAINT idx_url UNIQUE(url)
);
```

### Валидация JSON в базе данных

```sql
-- Добавить CHECK constraint для валидации JSON
ALTER TABLE web_documents ADD CONSTRAINT check_http_headers_json
CHECK (http_headers IS NULL OR JSON_VALID(http_headers));
```

## Расширение External Groups API

### Текущая структура API

External Groups API находится в `ee/tabby-webserver/src/routes/external_groups/` и имеет следующую структуру:

```
ee/tabby-webserver/src/routes/external_groups/
├── mod.rs                 # Роутинг
├── handlers.rs            # HTTP обработчики
├── models.rs              # Модели данных
├── graphql_wrapper.rs     # Обертка над GraphQL
└── docs/                  # Документация
```

### Существующие эндпоинты для источников

```rust
// В mod.rs
.route("/sources", get(handlers::list_sources))
.route("/sources", post(handlers::create_source))
.route("/sources", put(handlers::update_source))
.route("/sources", delete(handlers::delete_source))
```

### Предлагаемые новые эндпоинты для HTTP заголовков

#### 1. Обновление заголовков источника

**Эндпоинт**: `PUT /v1/external-groups/sources/headers`

**Назначение**: Обновление HTTP заголовков для существующего веб-документа

**Модель запроса**:
```rust
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct UpdateSourceHeadersRequest {
    /// ID источника (например: "custom_web_document:123")
    #[validate(length(min = 1, max = 100))]
    pub source_id: String,

    /// HTTP заголовки (опционально - для удаления передать null)
    pub http_headers: Option<Vec<HttpHeaderInput>>,
}

#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct HttpHeaderInput {
    /// Имя заголовка (например: "Authorization")
    #[validate(length(min = 1, max = 100))]
    #[validate(regex(path = "HEADER_NAME_REGEX", message = "Invalid header name"))]
    pub name: String,

    /// Значение заголовка (например: "Bearer token123")
    #[validate(length(min = 1, max = 1000))]
    pub value: String,
}
```

#### 2. Получение заголовков источника

**Эндпоинт**: `GET /v1/external-groups/sources/headers?source_id=custom_web_document:123`

**Назначение**: Получение текущих HTTP заголовков источника

**Модель ответа**:
```rust
#[derive(Debug, Serialize, ToSchema)]
pub struct SourceHeadersResponse {
    pub success: bool,
    pub data: SourceHeadersData,
}

#[derive(Debug, Serialize, ToSchema)]
pub struct SourceHeadersData {
    pub source_id: String,
    pub http_headers: Option<Vec<HttpHeaderOutput>>,
}

#[derive(Debug, Serialize, ToSchema)]
pub struct HttpHeaderOutput {
    pub name: String,
    pub value: String, // В продакшене может быть замаскировано
}
```

#### 3. Тестирование заголовков

**Эндпоинт**: `POST /v1/external-groups/sources/test-headers`

**Назначение**: Тестирование HTTP заголовков перед сохранением

**Модель запроса**:
```rust
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct TestSourceHeadersRequest {
    /// URL для тестирования
    #[validate(url)]
    pub url: String,

    /// HTTP заголовки для тестирования
    pub http_headers: Vec<HttpHeaderInput>,
}
```

### Обработчики (handlers.rs)

```rust
/// Обновление HTTP заголовков источника
pub async fn update_source_headers(
    State(ctx): State<Arc<dyn ServiceLocator>>,
    Json(request): Json<UpdateSourceHeadersRequest>,
) -> impl IntoResponse {
    info!("Updating source headers for: {}", request.source_id);

    if let Err(validation_errors) = request.validate() {
        warn!("Validation failed for update source headers: {:?}", validation_errors);
        return (
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse::validation_error(&validation_errors)),
        ).into_response();
    }

    let wrapper = GraphQLWrapper::new(ctx);

    match wrapper.update_source_headers(request).await {
        Ok(_) => {
            info!("Successfully updated source headers");
            (StatusCode::OK, Json(serde_json::json!({"success": true}))).into_response()
        }
        Err(error_msg) => {
            error!("Failed to update source headers: {}", error_msg);
            if error_msg.contains("not found") {
                (
                    StatusCode::NOT_FOUND,
                    Json(ErrorResponse::new("SOURCE_NOT_FOUND", &error_msg)),
                ).into_response()
            } else {
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse::internal_error()),
                ).into_response()
            }
        }
    }
}

/// Получение HTTP заголовков источника
pub async fn get_source_headers(
    State(ctx): State<Arc<dyn ServiceLocator>>,
    Query(params): Query<SourceHeadersQuery>,
) -> impl IntoResponse {
    info!("Getting source headers for: {}", params.source_id);

    let wrapper = GraphQLWrapper::new(ctx);

    match wrapper.get_source_headers(params.source_id).await {
        Ok(headers) => {
            info!("Successfully retrieved source headers");
            (StatusCode::OK, Json(SourceHeadersResponse {
                success: true,
                data: headers,
            })).into_response()
        }
        Err(error_msg) => {
            error!("Failed to get source headers: {}", error_msg);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse::internal_error()),
            ).into_response()
        }
    }
}

/// Тестирование HTTP заголовков
pub async fn test_source_headers(
    State(ctx): State<Arc<dyn ServiceLocator>>,
    Json(request): Json<TestSourceHeadersRequest>,
) -> impl IntoResponse {
    info!("Testing source headers for URL: {}", request.url);

    if let Err(validation_errors) = request.validate() {
        warn!("Validation failed for test source headers: {:?}", validation_errors);
        return (
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse::validation_error(&validation_errors)),
        ).into_response();
    }

    // Тестирование через HTTP клиент
    match test_http_headers(&request.url, &request.http_headers).await {
        Ok(test_result) => {
            info!("Header test completed successfully");
            (StatusCode::OK, Json(test_result)).into_response()
        }
        Err(error_msg) => {
            error!("Header test failed: {}", error_msg);
            (
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse::new("TEST_FAILED", &error_msg)),
            ).into_response()
        }
    }
}
```

### GraphQL Wrapper расширения

```rust
impl GraphQLWrapper {
    /// Обновление HTTP заголовков источника
    pub async fn update_source_headers(
        &self,
        request: UpdateSourceHeadersRequest,
    ) -> Result<(), String> {
        // Парсинг source_id для определения типа источника
        if request.source_id.starts_with("custom_web_document:") {
            let doc_id = request.source_id
                .strip_prefix("custom_web_document:")
                .ok_or("Invalid source_id format")?
                .parse::<i64>()
                .map_err(|_| "Invalid document ID")?;

            // Обновление через WebDocumentService
            let headers_json = request.http_headers
                .map(|headers| serde_json::to_string(&headers))
                .transpose()
                .map_err(|e| format!("Failed to serialize headers: {}", e))?;

            self.locator
                .web_document()
                .update_web_document_headers(doc_id, headers_json)
                .await
                .map_err(|e| format!("Failed to update headers: {}", e))?;

            Ok(())
        } else {
            Err("Only web documents support HTTP headers".to_string())
        }
    }

    /// Получение HTTP заголовков источника
    pub async fn get_source_headers(
        &self,
        source_id: String,
    ) -> Result<SourceHeadersData, String> {
        if source_id.starts_with("custom_web_document:") {
            let doc_id = source_id
                .strip_prefix("custom_web_document:")
                .ok_or("Invalid source_id format")?
                .parse::<i64>()
                .map_err(|_| "Invalid document ID")?;

            let doc = self.locator
                .web_document()
                .get_web_document(doc_id)
                .await
                .map_err(|e| format!("Failed to get document: {}", e))?;

            let headers = doc.http_headers
                .map(|json| serde_json::from_str(&json))
                .transpose()
                .map_err(|e| format!("Failed to parse headers: {}", e))?;

            Ok(SourceHeadersData {
                source_id,
                http_headers: headers,
            })
        } else {
            Err("Only web documents support HTTP headers".to_string())
        }
    }
}
```

### Безопасность и валидация

#### Валидация заголовков

```rust
use regex::Regex;

lazy_static! {
    static ref HEADER_NAME_REGEX: Regex = Regex::new(r"^[a-zA-Z0-9\-_]+$").unwrap();
}

fn validate_header_security(headers: &[HttpHeaderInput]) -> Result<(), String> {
    for header in headers {
        // Проверка безопасных имен заголовков
        let name_lower = header.name.to_lowercase();

        // Запрещенные заголовки
        if matches!(name_lower.as_str(),
            "host" | "content-length" | "connection" | "upgrade" | "proxy-authorization"
        ) {
            return Err(format!("Header '{}' is not allowed", header.name));
        }

        // Проверка на потенциально опасные значения
        if header.value.contains('\n') || header.value.contains('\r') {
            return Err("Header values cannot contain newlines".to_string());
        }

        // Ограничение размера
        if header.value.len() > 1000 {
            return Err("Header value too long (max 1000 characters)".to_string());
        }
    }

    Ok(())
}
```

#### Маскировка чувствительных данных

```rust
fn mask_sensitive_headers(headers: &mut [HttpHeaderOutput]) {
    for header in headers {
        let name_lower = header.name.to_lowercase();
        if matches!(name_lower.as_str(),
            "authorization" | "x-api-key" | "cookie" | "x-auth-token"
        ) {
            // Маскировка: показываем только первые 8 символов
            if header.value.len() > 8 {
                header.value = format!("{}***", &header.value[..8]);
            } else {
                header.value = "***".to_string();
            }
        }
    }
}
```

### Роутинг (mod.rs)

```rust
pub fn create_external_groups_router_new(ctx: Arc<dyn ServiceLocator>) -> Router {
    Router::new()
        // ... существующие роуты ...

        // HTTP заголовки источников
        .route("/sources/headers", get(handlers::get_source_headers))
        .route("/sources/headers", put(handlers::update_source_headers))
        .route("/sources/test-headers", post(handlers::test_source_headers))

        // ... остальные роуты ...
        .with_state(ctx)
        .layer(middleware::from_fn(require_admin_auth_token))
        .layer(RateLimitLayer::new(120, Duration::from_secs(60)))
}

## Задачи для Jira (по порядку выполнения)

### Этап 1: Подготовка базы данных

#### Задача 1: Создание миграции для HTTP заголовков
**Приоритет**: High
**Оценка**: 2 SP
**Описание**: Создать миграцию для добавления поля `http_headers` в таблицу `web_documents`

**Критерии приемки**:
- [ ] Создан файл миграции `0048_add-http-headers-to-web-documents.up.sql`
- [ ] Создан файл отката `0048_add-http-headers-to-web-documents.down.sql`
- [ ] Добавлен CHECK constraint для валидации JSON
- [ ] Создан индекс для оптимизации запросов
- [ ] Миграция протестирована локально

**Файлы для изменения**:
- `ee/tabby-db/migrations/0048_add-http-headers-to-web-documents.up.sql` (новый)
- `ee/tabby-db/migrations/0048_add-http-headers-to-web-documents.down.sql` (новый)

#### Задача 2: Обновление DAO для работы с HTTP заголовками
**Приоритет**: High
**Оценка**: 3 SP
**Описание**: Обновить структуры данных и методы DAO для поддержки HTTP заголовков

**Критерии приемки**:
- [ ] Обновлена структура `WebDocumentDAO` с полем `http_headers`
- [ ] Обновлены методы `create_web_document` и `update_web_document`
- [ ] Добавлен метод `update_web_document_headers`
- [ ] Добавлены unit тесты для новых методов
- [ ] Проверена совместимость с существующими данными

**Файлы для изменения**:
- `ee/tabby-db/src/web_documents.rs`
- `ee/tabby-db/src/migration_tests.rs`

### Этап 2: Обновление GraphQL API

#### Задача 3: Расширение GraphQL схемы для HTTP заголовков
**Приоритет**: High
**Оценка**: 3 SP
**Описание**: Добавить поддержку HTTP заголовков в GraphQL схему и типы данных

**Критерии приемки**:
- [ ] Создан тип `HttpHeaderInput` для входных данных
- [ ] Обновлен `CreateCustomDocumentInput` с полем `http_headers`
- [ ] Добавлен `UpdateWebDocumentHeadersInput` для обновления заголовков
- [ ] Обновлены резолверы для обработки заголовков
- [ ] Добавлена валидация заголовков

**Файлы для изменения**:
- `ee/tabby-schema/src/schema/web_documents.rs`
- `ee/tabby-schema/src/lib.rs`

#### Задача 4: Обновление WebDocumentService
**Приоритет**: High
**Оценка**: 4 SP
**Описание**: Обновить сервис для работы с HTTP заголовками и передачи их в краулер

**Критерии приемки**:
- [ ] Обновлен метод `create_custom_web_document` для поддержки заголовков
- [ ] Добавлен метод `update_web_document_headers`
- [ ] Обновлена передача заголовков в `WebCrawlerJob`
- [ ] Добавлена валидация и санитизация заголовков
- [ ] Добавлены unit тесты

**Файлы для изменения**:
- `ee/tabby-webserver/src/service/web_documents.rs`

### Этап 3: Обновление краулера

#### Задача 5: Добавление поддержки HTTP заголовков в краулер
**Приоритет**: High
**Оценка**: 4 SP
**Описание**: Обновить краулер для передачи HTTP заголовков в Katana

**Критерии приемки**:
- [ ] Обновлен метод `crawl_pipeline` для поддержки заголовков
- [ ] Добавлена функция `crawl_pipeline_with_headers`
- [ ] Реализована передача заголовков через флаг `-H` в Katana
- [ ] Добавлена обработка ошибок аутентификации
- [ ] Добавлены integration тесты

**Файлы для изменения**:
- `crates/tabby-crawler/src/lib.rs`

#### Задача 6: Обновление WebCrawlerJob для HTTP заголовков
**Приоритет**: High
**Оценка**: 3 SP
**Описание**: Обновить фоновую задачу краулинга для передачи заголовков

**Критерии приемки**:
- [ ] Обновлена структура `WebCrawlerJob` с полем заголовков
- [ ] Обновлен метод `run` для передачи заголовков в краулер
- [ ] Добавлена обработка ошибок аутентификации
- [ ] Обновлено логирование для безопасности (маскировка токенов)
- [ ] Добавлены тесты

**Файлы для изменения**:
- `ee/tabby-webserver/src/service/background_job/web_crawler.rs`

### Этап 4: External Groups API

#### Задача 7: Добавление моделей для HTTP заголовков в External API
**Приоритет**: Medium
**Оценка**: 2 SP
**Описание**: Создать модели данных для работы с HTTP заголовками через External Groups API

**Критерии приемки**:
- [ ] Создан `HttpHeaderInput` и `HttpHeaderOutput`
- [ ] Создан `UpdateSourceHeadersRequest`
- [ ] Создан `SourceHeadersResponse` и `SourceHeadersData`
- [ ] Создан `TestSourceHeadersRequest`
- [ ] Добавлена валидация и документация OpenAPI

**Файлы для изменения**:
- `ee/tabby-webserver/src/routes/external_groups/models.rs`

#### Задача 8: Реализация обработчиков для HTTP заголовков
**Приоритет**: Medium
**Оценка**: 5 SP
**Описание**: Создать HTTP обработчики для управления заголовками через External API

**Критерии приемки**:
- [ ] Реализован `update_source_headers` handler
- [ ] Реализован `get_source_headers` handler
- [ ] Реализован `test_source_headers` handler
- [ ] Добавлена валидация безопасности заголовков
- [ ] Добавлена маскировка чувствительных данных
- [ ] Добавлены unit тесты

**Файлы для изменения**:
- `ee/tabby-webserver/src/routes/external_groups/handlers.rs`

#### Задача 9: Расширение GraphQL Wrapper для HTTP заголовков
**Приоритет**: Medium
**Оценка**: 3 SP
**Описание**: Добавить методы в GraphQL Wrapper для работы с HTTP заголовками

**Критерии приемки**:
- [ ] Реализован метод `update_source_headers`
- [ ] Реализован метод `get_source_headers`
- [ ] Добавлена обработка ошибок
- [ ] Добавлена поддержка только веб-документов
- [ ] Добавлены unit тесты

**Файлы для изменения**:
- `ee/tabby-webserver/src/routes/external_groups/graphql_wrapper.rs`

#### Задача 10: Добавление роутинга для HTTP заголовков
**Приоритет**: Medium
**Оценка**: 1 SP
**Описание**: Добавить новые эндпоинты в роутинг External Groups API

**Критерии приемки**:
- [ ] Добавлен роут `GET /sources/headers`
- [ ] Добавлен роут `PUT /sources/headers`
- [ ] Добавлен роут `POST /sources/test-headers`
- [ ] Проверена работа middleware аутентификации
- [ ] Обновлена документация API

**Файлы для изменения**:
- `ee/tabby-webserver/src/routes/external_groups/mod.rs`

### Этап 5: Frontend (опционально)

#### Задача 11: Обновление веб-интерфейса для HTTP заголовков
**Приоритет**: Low
**Оценка**: 8 SP
**Описание**: Добавить поля для HTTP заголовков в форму создания веб-документов

**Критерии приемки**:
- [ ] Добавлены поля для ввода HTTP заголовков
- [ ] Реализовано динамическое добавление/удаление заголовков
- [ ] Добавлена валидация на frontend
- [ ] Добавлена маскировка чувствительных данных
- [ ] Обновлены GraphQL запросы
- [ ] Добавлены тесты компонентов

**Файлы для изменения**:
- `ee/tabby-ui/app/(dashboard)/settings/(integrations)/providers/doc/components/create-custom-doc.tsx`
- Возможно новые компоненты для управления заголовками

### Этап 6: Тестирование и документация

#### Задача 12: Создание интеграционных тестов
**Приоритет**: Medium
**Оценка**: 5 SP
**Описание**: Создать комплексные тесты для проверки работы HTTP заголовков

**Критерии приемки**:
- [ ] Тесты создания веб-документа с заголовками
- [ ] Тесты обновления заголовков через External API
- [ ] Тесты краулинга с аутентификацией
- [ ] Тесты безопасности (валидация, маскировка)
- [ ] Тесты обработки ошибок
- [ ] Документация по тестированию

**Файлы для изменения**:
- `ee/tabby-webserver/src/routes/external_groups/tests/` (новые файлы)
- Обновление существующих тестов

#### Задача 13: Обновление документации
**Приоритет**: Low
**Оценка**: 3 SP
**Описание**: Обновить документацию API и архитектуры

**Критерии приемки**:
- [ ] Обновлена документация External Groups API
- [ ] Добавлены примеры использования HTTP заголовков
- [ ] Обновлена архитектурная документация
- [ ] Добавлены рекомендации по безопасности
- [ ] Создан troubleshooting guide

**Файлы для изменения**:
- `ee/tabby-webserver/src/routes/external_groups/docs/API_DOCUMENTATION.md`
- `ee/tabby-webserver/src/routes/external_groups/docs/FILIN_GROUPS_ARCHITECTURE.md`

## Общая оценка проекта

**Общая сложность**: 42 Story Points
**Время реализации**: 2-3 недели (при работе 1 разработчика)
**Критический путь**: Задачи 1-6 (база данных → GraphQL → краулер)
**Риски**: Минимальные, архитектура поддерживает расширение

## Порядок выполнения задач

### Неделя 1: Основная функциональность
1. **День 1-2**: Задачи 1-2 (База данных)
2. **День 3-4**: Задачи 3-4 (GraphQL API)
3. **День 5**: Задачи 5-6 (Краулер)

### Неделя 2: External API
1. **День 1-2**: Задачи 7-9 (External Groups API)
2. **День 3**: Задача 10 (Роутинг)
3. **День 4-5**: Задача 12 (Тестирование)

### Неделя 3: Доработки (опционально)
1. **День 1-3**: Задача 11 (Frontend)
2. **День 4-5**: Задача 13 (Документация)

## Критерии готовности (Definition of Done)

Для каждой задачи:
- [ ] Код написан и протестирован
- [ ] Unit тесты покрывают новую функциональность
- [ ] Integration тесты проходят
- [ ] Код прошел code review
- [ ] Документация обновлена
- [ ] Миграции протестированы на тестовой среде
- [ ] Безопасность проверена (валидация, маскировка)
- [ ] Логирование добавлено с правильным уровнем
- [ ] Обработка ошибок реализована
```
