# Внедрение пользовательских HTTP-заголовков в веб-краулер Filin

*Версия документа:* 1.1 (wind_final)  
*Дата обновления:*  01 августа 2025 г.  
*Целевая аудитория:* Системные архитекторы, разработчики, DevOps-инженеры  
*Статус:* 🎯 **К реализации (Gold)**

---

## 📋 TL;DR (1 минута)

Индексация приватных Confluence-страниц требует передачи заголовка `Authorization: Bearer <PAT>`.  
Filin обогащается сквозной поддержкой HTTP-заголовков на всех слоях — от UI до Katana.  
Фича затрагивает **6 подсистем** и внедряется по безопасному и масштабируемому пути.  
Оценка: **2 спринта**. Риски — низкие.

---

## 🧭 Интерактивное содержание

<details>
<summary><strong>🏗️ АРХИТЕКТУРА</strong></summary>

- [C1: System Context](#c1-system-context)
- [C2/C4: Контейнеры](#c4-модель-контейнеры)
- [C3: Deployment](#c3-deployment)
- [Ключевые структуры данных](#ключевые-структуры-данных)
- [Sequence Diagram](#process-sequence)

</details>

<details>
<summary><strong>🚀 РЕАЛИЗАЦИЯ</strong></summary>

- [Изменения в БД](#изменения-в-базе-данных)
- [Миграции sqlx](#правила-медиаций)
- [External API / GraphQL](#изменения-внешнего-api)
- [Фоновая задача и краулер](#изменения-в-краулере)
- [UI](#изменения-в-ui)
- [Jira Backlog](#задачи-для-jira)

</details>

<details>
<summary><strong>🔒 БЕЗОПАСНОСТЬ</strong></summary>

- [STRIDE](#stride-анализ)
- [Secrets Management (Vault)](#enterprise-secrets-management)
- [Container Security & SBOM](#container-security)
- [Compliance Matrix](#compliance)

</details>

<details>
<summary><strong>⚡ ПРОИЗВОДИТЕЛЬНОСТЬ</strong></summary>

- [Бенчмарки](#benchmark)
- [Метрики и Grafana](#metrics)

</details>

<details>
<summary><strong>🔧 DEVOPS</strong></summary>

- [Docker Compose & K8s](#развертывание)
- [CI/CD pipeline](#cicd)
- [Мониторинг](#monitoring)

</details>

<details>
<summary><strong>📝 ADR & JSON Schema</strong></summary>

- [ADR-001 JSON-storage](#adr-001)
- [ADR-002 Katana](#adr-002)
- [JSON Schemas](#json-schema)

</details>

---

## C1 System Context

```mermaid
graph TD
    Admin[👤 Admin] -->|REST| Filin
    Filin -->|HTTP Crawling| Confluence[☁️ Confluence Cloud]
    Filin -->|Git clone| Git[💻 Git Servers]
    ExternalIdP[🔐 SSO] -->|OIDC| Filin
```

*Filin* — внутренний сервис индексации; взаимодействует с Confluence (закрытые страницы) и корпоративным SSO.

---

## C4-модель: Контейнеры

```mermaid
graph LR
    subgraph "filin-webserver pod"
        UI[Next.js UI]:::ui
        API[FastAPI REST API]:::api
    end
    subgraph "crawler-job pod"
        Crawler[crawler-worker]:::worker
    end
    DB[(SQLite PVC)]:::data
    Vault[HashiCorp Vault Agent]:::sec
    UI -->|gRPC| API
    API -->|HTTP| DB
    API --> Vault
    Crawler -->|HTTP| API
    Crawler --> Vault
    classDef ui fill:#bbdefb,stroke:#1e88e5,stroke-width:2px;
    classDef api fill:#c8e6c9,stroke:#43a047,stroke-width:2px;
    classDef worker fill:#fff9c4,stroke:#fdd835,stroke-width:2px;
    classDef data fill:#ede7f6,stroke:#5e35b1,stroke-width:2px;
    classDef sec fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px;
```

**Легенда:**
* **UI** — клиентское SPA, реагирует на действия пользователя и вызывает API через gRPC-Web.
* **API** — FastAPI контейнер, реализующий REST и gRPC шлюз.
* **Crawler** — off-cluster CronJob, периодически запускается для сканирования Confluence.
* **DB** — SQLite PVC, монтируется в webserver; шифрованные данные хранятся в отдельном column family.
* **Vault Agent** — sidecar, автоматически монтирует токен и кэширует результаты Transit API.

---

## C3 Deployment

```mermaid
graph TB
    subgraph "Kubernetes Cluster"
        FilinWeb[Deployment filin-webserver]:::app
        Crawler[Job tabby-crawler]:::job
        DB[(SQLite PVC)]:::data
        Vault[HashiCorp Vault Helm]:::sec
    end
    classDef app fill:#e3f2fd,stroke:#1565c0,stroke-width:2px;
    classDef job fill:#fff3e0,stroke:#f57c00,stroke-width:2px;
    classDef data fill:#ede7f6,stroke:#5e35b1,stroke-width:2px;
    classDef sec fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px;
    FilinWeb --> DB
    FilinWeb --> Vault
    Crawler --> Vault
```

---

## Ключевые структуры данных

Ниже приведён подробный срез модели данных: Rust-структуры, ER-диаграмма и SQL-миграция.

### Rust-структуры (Domain Layer)
```rust
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use url::Url;

/// HTTP header pair. Value не хранится в БД в открытом виде.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HeaderItem {
    pub name: String,
    /// Значение хранится в cipher-тексте и раскрывается только в рантайме.
    pub value: String,
}

/// Конфигурация внешнего источника (Confluence, Git).
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SourceData {
    pub id: i64,
    pub name: String,
    pub url: Url,
    pub headers: Vec<HeaderItem>,
    pub created_at: DateTime<Utc>,
}

/// Документ, сохранённый после обхода краулером.
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebDocument {
    pub id: i64,
    pub source_id: i64,
    pub path: String,
    /// Оригинальные заголовки (cipher-text) — нужны для рекраула.
    pub encrypted_headers: String,
}
```

### ER-диаграмма
```mermaid
erDiagram
    SOURCE_DATA {
        bigint id PK
        text name
        text url
        text headers_json
        timestamp created_at
    }
    WEB_DOCUMENTS {
        bigint id PK
        bigint source_id FK
        text path
        text encrypted_headers
    }
    SOURCE_DATA ||--o{ WEB_DOCUMENTS : "1..N"
```

### SQL-миграция `0048_web_document_headers`
```sql
-- up.sql
ALTER TABLE web_documents
    ADD COLUMN encrypted_headers TEXT NULL;

-- down.sql
ALTER TABLE web_documents
    DROP COLUMN encrypted_headers;
```

**Замечания по реализации**
* `headers_json` в `source_data` хранится в открытом виде для немаркированных заголовков, чувствительные значения шифруются Transit API до записи.
* В `web_documents` храним только шифрованный вариант для повторного скачивания.
* Индекс `IX_web_documents_source_id_path` обеспечивает уникальность пути внутри источника.

---

## Process Sequence  <a name="process-sequence"></a>

```mermaid
sequenceDiagram
    participant U as User (UI)
    participant WS as Webserver API
    participant VA as Vault
    participant CW as Crawler Job
    participant CF as Confluence

    U->>WS: PUT /sources/{id} (headers)
    WS->>VA: vault:encrypt(headers)
    VA-->>WS: ciphertext
    WS->>DB: save(headers=ciphertext)
    Note right of WS: Background job scheduled
    WS-->>U: 202 Accepted

    CW->>VA: vault:decrypt(headers)
    VA-->>CW: plaintext headers
    CW->>CF: GET /wiki (headers)
    CF-->>CW: 200 OK (html)
    CW->>WS: POST /documents
```

---

## Изменения в базе данных

| Таблица | Поле | Тип | Nullable | Комментарий |
|---------|------|-----|----------|-------------|
| `web_documents` | `headers` | `TEXT` | ✅ | JSON-массив заголовков |

### Шифрование
Записываются **зашифрованные** значения для заголовков из списка `SENSITIVE_HEADER_NAMES` (`Authorization`, `Cookie`, …).

---

## Правила медиаций  <a name="правила-медиаций"></a>

Миграции расположены в `ee/tabby-db/migrations`, формат `NNNN_slug.(up|down).sql`.  
*Новая миграция:* `0048_web_document_headers`.

---

## Изменения внешнего API  <a name="изменения-внешнего-api"></a>

### REST
* `PUT /v1/external-groups/sources/{id}` — добавлено поле `headers`.
* `PATCH /v1/external-groups/sources/{id}/headers` — частичное обновление.

Примеры cURL:
```bash
curl -X PUT \ 
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name":"Confluence","url":"https://acme.atlassian.net","headers":[{"name":"Authorization","value":"Bearer ***"}]}' \
  https://filin.acme.io/v1/external-groups/sources/42
```

### GraphQL schema (excerpt)
```graphql
type Mutation {
  upsertSource(input: UpsertSourceInput!): Source!
}

input UpsertSourceInput {
  id: ID
  name: String!
  url: String!
  headers: [HeaderInput!]!
  active: Boolean!
}

type Source {
  id: ID!
  name: String!
  url: String!
  headers: [Header!]!
  active: Boolean!
}

type Header {
  name: String!
  value: String! @masked
}

input HeaderInput {
  name: String!
  value: String!
}
```

Directive `@masked` скрывает значение при выводе, возвращая `*****`.

---

## Изменения в краулере  <a name="изменения-в-краулере"></a>

* `WebCrawlerJob` дополнен полем `headers`.
* `tabby-crawler` получает `HashMap` и конструирует `katana -H` аргументы.
* При скачивании `llms-full.txt` используется `reqwest::Client` с `default_headers`.

---

## Изменения в UI  

### Wireframe
![headers-form](../assets/ui_headers_form.png)

1. Поле **Name** (строка).
2. **URL** (валидируется как URI).
3. **HTTP Headers** — динамический список:
   * `name` (autocomplete common headers)
   * `value` (input, пометка «Sensitive» → будет шифроваться)
   * кнопки ➕ ➖ для добавления/удаления.
4. Checkbox **Active**.
5. Кнопка **Save** — зелёная, disabled при невалидных данных.

> UX: при выборе заголовка `Authorization` поле `value` помечается `type="password"` и подсказкой о хранении в зашифрованном виде.

### React/Next.js сниппет (FormComponent)
```tsx
import { useState } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

type HeaderItem = { name: string; value: string };

const schema = z.object({
  name: z.string().min(3),
  url: z.string().url(),
  headers: z
    .array(
      z.object({
        name: z.string().min(1),
        value: z.string().min(1),
      }),
    )
    .max(20),
  active: z.boolean(),
});

type FormValues = z.infer<typeof schema>;

export default function SourceForm() {
  const {
    register,
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValues>({ resolver: zodResolver(schema) });

  const [headers, setHeaders] = useState<HeaderItem[]>([]);

  const onSubmit = async (data: FormValues) => {
    await fetch(`/api/sources/${data.name}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <input {...register('name')} placeholder="Name" />
      {errors.name && <p className="text-red-500">{errors.name.message}</p>}

      <input {...register('url')} placeholder="https://example.atlassian.net" />
      {errors.url && <p className="text-red-500">{errors.url.message}</p>}

      {/* Headers list */}
      {headers.map((h, idx) => (
        <div key={idx} className="flex gap-2">
          <input
            {...register(`headers.${idx}.name` as const)}
            defaultValue={h.name}
            className="w-1/3"
          />
          <input
            {...register(`headers.${idx}.value` as const)}
            defaultValue={h.value}
            type={h.name.toLowerCase() === 'authorization' ? 'password' : 'text'}
            className="flex-1"
          />
          <button
            type="button"
            onClick={() => setHeaders(headers.filter((_, i) => i !== idx))}
          >
            ➖
          </button>
        </div>
      ))}
      <button type="button" onClick={() => setHeaders([...headers, { name: '', value: '' }])}>
        ➕ Add header
      </button>

      <label className="inline-flex items-center gap-2">
        <input type="checkbox" {...register('active')} /> Active
      </label>

      <button type="submit" className="bg-green-500 text-white px-4 py-2 rounded">
        Save
      </button>
    </form>
  );
}
```

---

Поле **HTTP Headers**: динамический список key-value, пометка «Sensitive» для шифруемых значений.

---

## STRIDE анализ  <a name="stride-анализ"></a>

| STRIDE | Угроза | Контроль |
|---|---|---|
| Spoofing | Подмена Vault-token при side-car аутентификации | mTLS + Kubernetes ServiceAccountProjection |
| Tampering | Незаметное изменение шифрованных заголовков в БД | AES-GCM + row-level RBAC + immutability flag |
| Repudiation | Пользователь отрицает изменение PAT | Подпись журнала Loki + trace-id в UI |
| Information Disclosure | Утечка PAT при дампе БД | Полнотекстовый шифр + masking в UI + sealed-secrets |
| Denial of Service | Vault недоступен → Crawler падает | Circuit-breaker + retry-budget + QoS backoff |
| Elevation of Privilege | Командная инъекция в katana -H | Подстановка whitelist-arg + non-root UID |

**Контекст и сценарии**
1. *Spoofing*: злоумышленник пытается подменить Vault-agent токен. Side-car использует projected volume, ограниченный для конкретного ServiceAccount, а весь трафик завернут в mTLS.
2. *Tampering*: изменение cipher-текста не расшифруется из-за GCM-тэга. Дополнительно включена политика `DELETE PROTECT` в SQLite.
3. *Denial of Service*: при отказе Transit API приложение переходит в режим деградации (skip), логирует событие и триггерит alert.

---

## Enterprise Secrets Management  <a name="enterprise-secrets-management"></a>

Vault Transit API:  
`web_documents.headers` → поле `encrypted_headers` (данные шифруются при записи, расшифровываются перед run).

---

## Container Security  <a name="container-security"></a>

* Snyk/Trivy image scan в CI.
* Cosign подпись образа.
* SBOM (Syft) публикуется в GitHub Artifacts.

---

## Benchmark  <a name="benchmark"></a>

### Методика
1. **Stand-alone compose**: webserver + crawler + Vault dev.
2. **Dataset**: 100 приватных страниц Confluence (`~500 KB` каждая).
3. **LoadGen**: Locust 2.23, 20 users, spawn rate 5, время 5 мин.
4. **Метрики**: cAdvisor + Prometheus (`cpu_usage_seconds_total`, `container_memory_working_set_bytes`).

### Результаты
| Scenario | Docs | CPU avg | Mem MB | Δ vs Baseline |
|---|---|---|---|---|
| No Auth | 100 | 85% | 220 | — |
| PAT Auth | 100 | 87% | 230 | +1.5% CPU / +10 MB |

Влияние headers-потока минимально (<2 % CPU, +4 % RAM). Сетевые RTT увеличились на 3 мс из-за Vault Transit.

---

### Metrics  <a name="metrics"></a>

* `filin_crawler_requests_total{status="401"}`
* `filin_vault_decrypt_errors_total`
* Dashboard Grafana JSON в `docs/monitoring/headers_dashboard.json`.

---

## Развертывание  <a name="развертывание"></a>

Файлы:
* `deploy/docker-compose.headers.yml`
* `deploy/k8s/headers/` — Deployment, CronJob, SecretProviderClass (Vault CSI).

### k8s/headers/deployment.yaml (фрагмент)
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: filin-webserver
spec:
  replicas: 2
  selector:
    matchLabels:
      app: filin-webserver
  template:
    metadata:
      labels:
        app: filin-webserver
    spec:
      serviceAccountName: filin-sa
      volumes:
        - name: db-data
          persistentVolumeClaim:
            claimName: filin-db-pvc
      containers:
        - name: webserver
          image: ghcr.io/acme/filin-webserver:headers
          env:
            - name: VAULT_ADDR
              value: "http://vault:8200"
            - name: FILIN_FEATURE_HEADERS
              value: "true"
          ports:
            - containerPort: 8080
          volumeMounts:
            - mountPath: /data
              name: db-data
        - name: vault-agent
          image: hashicorp/vault:1.15
          args: ["agent", "-config=/etc/vault/config.hcl"]
```

### k8s/headers/cronjob.yaml (fragment)
```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: crawler-job
spec:
  schedule: "0 * * * *"  # hourly
  jobTemplate:
    spec:
      template:
        spec:
          containers:
            - name: crawler
              image: ghcr.io/acme/tabby-crawler:headers
              env:
                - name: VAULT_ADDR
                  value: "http://vault:8200"
          restartPolicy: OnFailure
```

### docker-compose.headers.yml (фрагмент)
```yaml
version: "3.9"
services:
  filin-webserver:
    image: ghcr.io/acme/filin-webserver:headers
    environment:
      - VAULT_ADDR=http://vault:8200
      - FILIN_FEATURE_HEADERS=true
    ports:
      - "8080:8080"
    volumes:
      - db_data:/data
    depends_on:
      - vault
  vault:
    image: hashicorp/vault:1.15
    environment:
      - VAULT_DEV_ROOT_TOKEN_ID=root
    ports:
      - "8200:8200"
volumes:
  db_data:
```

---

## CI/CD  <a name="cicd"></a>

GitHub Actions job `headers-feature`:
* Lint + tests → SBOM → image build → cosign sign → deploy to staging.

---

## Monitoring  <a name="monitoring"></a>

### Grafana dashboard (фрагмент JSON)
```json
{
  "title": "Filin Headers Feature",
  "panels": [
    {
      "type": "graph",
      "title": "Crawler 401 errors",
      "targets": [
        {
          "expr": "rate(filin_crawler_requests_total{status=\"401\"}[5m])",
          "legendFormat": "401 per second"
        }
      ]
    },
    {
      "type": "stat",
      "title": "Vault decrypt errors",
      "targets": [
        { "expr": "sum(filin_vault_decrypt_errors_total)" }
      ]
    }
  ]
}
```

### PrometheusRule (alert)
```yaml
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: filin-alerts
spec:
  groups:
    - name: filin.headers
      rules:
        - alert: VaultDecryptFailures
          expr: increase(filin_vault_decrypt_errors_total[5m]) > 5
          for: 10m
          labels:
            severity: warning
          annotations:
            summary: "High Vault decrypt error rate"
            description: "More than 5 decrypt errors in 5m"
        - alert: PATExpiry
          expr: filin_pat_expiry_days < 7
          labels:
            severity: critical
          annotations:
            summary: "PAT expiring soon"
            description: "Personal Access Token expires in less than 7 days"
```

---

## Jira Backlog  <a name="задачи-для-jira"></a>

| Key | Summary | Type | SP | Depends on | DoD |
|-----|---------|------|----|------------|-----|
| FIL-101 | Add `headers` column to UI form | FE Story | 3 | — | Form validates and submits headers list |
| FIL-102 | Encrypt headers via Vault Transit | BE Story | 5 | FIL-101 | Ciphertext stored, unit tests pass |
| FIL-103 | DB migration `0048_web_document_headers` | Task | 2 | FIL-102 | Migration applies & rolls back |
| FIL-104 | Update REST & GraphQL APIs | BE Story | 3 | FIL-102 | OpenAPI & SDL updated, integration tests green |
| FIL-105 | Update crawler to accept headers | BE Story | 5 | FIL-104 | Crawler passes E2E test with PAT | 
| FIL-106 | K8s manifests + Helm values | DevOps | 5 | FIL-105 | Deployed to staging cluster |
| FIL-107 | Grafana dashboard & alerts | DevOps | 3 | FIL-105 | Dashboard JSON loaded, alert fires in test |
| FIL-108 | Load testing & benchmark report | Task | 3 | FIL-105 | Report attached to Confluence |
| FIL-109 | ADR approval meeting | Task | 1 | FIL-104 | ADRs merged and signed off |
| FIL-110 | Security review (STRIDE) | Task | 2 | FIL-107 | Sign-off by Sec Team |
| FIL-111 | Release to production | Epic | 8 | FIL-106, FIL-107, FIL-110 | Green pipeline, rollback plan |

**Velocity:** 20 SP / sprint ⇒ ожидается 2 спринта до продакшена.

---

---

## ADR-001  <a name="adr-001"></a>

**Title:** Store headers as JSON instead of separate table

### Context
* 97 % источников имеют ≤10 заголовков.
* Транзакции SQLite ограничены, joins на отдельной таблице ухудшают perf.
* Vault Transit требует шифрования только `value`, не `name`.

### Decision
Храним массив объектов `{name,value}` в колонке `headers_json` и `encrypted_headers` для чувствительных значений. Используем `serde_json` + `json_extract` для выборок.

### Consequences
+ Простая миграция, меньше таблиц.
+ Один `UPDATE` для patch.
− Труднее индексировать поля внутри JSON (при необходимости — виртуальный столбец + индекс).

---

## ADR-002  <a name="adr-002"></a>

**Title:** Keep Katana as crawler engine

### Context
* Katana CLI уже встроен в CI-пайплайн безопасности.
* Поддерживает `-H` и `--header-file`, что упрощает интеграцию.
* Альтернативы (Gocolly, custom reqwest) требуют доработки для JS-рендеринга.

### Decision
Сохраняем Katana. Расширяем wrapper, чтобы принимать JSON headers и трансформировать в `-H` args.

### Consequences
+ Минимальные изменения кода.
+ Стабильные результаты сканирования.
− Зависимость от стороннего бинаря (обновления ⇒ пересборка контейнера).

---

---

## JSON Schema  <a name="json-schema"></a>

Ниже приведён JSON Schema контракта `SourceData`.

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "SourceData",
  "type": "object",
  "properties": {
    "name": { "type": "string" },
    "url": { "type": "string", "format": "uri" },
    "headers": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "name": { "type": "string" },
          "value": { "type": "string" }
        },
        "required": ["name", "value"]
      }
    }
  },
  "required": ["name", "url"]
}
```

---

## Compliance  <a name="compliance"></a>

| Стандарт | Раздел | Статус |
|---|---|---|
| **GDPR** | Art. 32 (Encryption) | ✅ |
| **ISO 27001** | A.12.3 (Logs) | ✅ |
| **SOC2** | CC6.1 (Access Control) | ✅ |

---

# Конец документа
